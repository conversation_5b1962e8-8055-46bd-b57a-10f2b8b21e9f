import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useHisotryStore = defineStore('history', () => {
  const cities = ref([])

  uni.getStorage({
    key: 'cities',
    success: (res) => {
      if (res.data) {
        cities.value = res.data
      }
    }
  })

  function addCity(city) {
    const index = cities.value.findIndex(item => item.id === city.id)
    if (index !== -1) {
      return
    }

    cities.value.unshift(city)

    if (cities.value.length > 10) {
      cities.value.splice(10)
    }

    uni.setStorage({
      key: 'cities',
      data: cities.value
    })
  }

  return {
    cities,
    addCity
  }

})