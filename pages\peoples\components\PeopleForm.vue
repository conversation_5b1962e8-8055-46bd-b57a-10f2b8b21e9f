<template>
  <MyInfoPopup action-text="保存" title="出行人" type="bottom" @action="onSubmit" @close="show = false">
    <uni-forms ref="form" :model="model" :rules="rules" label-width="120">
      <uni-forms-item label="证件类型" name="id_type" required>
        <uni-data-select v-model="model.id_type" :localdata="idTypes"></uni-data-select>
      </uni-forms-item>
      <uni-forms-item label="中文姓名" name="name" required>
        <uni-easyinput v-model="model.name" placeholder="与证件姓名一致"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item label="证件号码" name="id_no" required>
        <uni-easyinput v-model="model.id_no"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item label="手机号码" name="phone">
        <uni-easyinput v-model="model.phone" placeholder="常用手机号，选填"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item>
        <view class="default">
          设为默认
          <switch :checked="model.is_default == Enable" @change="onIsDefault"/>
        </view>
      </uni-forms-item>
      <uni-forms-item>
        <view class="protocol">
          <yj-checkbox-group v-model="isAg">
            <yj-checkbox :value="true" label="阅读并同意以下内容"/>
          </yj-checkbox-group>
          <view class="txt">
            你已知晓你填写的上述信息将被用于预定实名制的产品，请确保信息真实有效。仅在具体交易时授权提供给第三方。
          </view>
        </view>

      </uni-forms-item>
    </uni-forms>

    <view v-if="isEdit" class="remove">
      <view @tap="onRemove">
        <text class="iconfont icon-shanchu"></text>
        删除
      </view>

    </view>

  </MyInfoPopup>
</template>

<script setup>
import {peopleOptions, peopleRemove, peopleSave} from '@/api/user'
import MyInfoPopup from '@/components/MyInfoPopup/MyInfoPopup.vue'
import {showConfirm, showToast} from '@/utils';
import {Disable, Enable} from '@/utils/constmap';
import {computed, onMounted, ref, watch} from 'vue'
import YjCheckboxGroup from "@/components/YjCheckbox/YjCheckboxGroup.vue";
import YjCheckbox from "@/components/YjCheckbox/YjCheckbox.vue";

const emit = defineEmits(['close', 'remove'])
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const form = ref(null)
const show = ref(true)
const model = ref({
  name: props.data.name ?? '',
  phone: props.data.phone ?? '',
  id_no: props.data.id_no ?? '',
  id_type: props.data.id_type ?? '',
  is_default: props.data.is_default ?? Disable,
  id: props.data.id ?? 0
})
const rules = ref({
  name: {
    rules: [
      {required: true, errorMessage: '请填写中文姓名'}
    ]
  },
  id_type: {
    rules: [
      {required: true, errorMessage: '请选择证件类型'}
    ]
  },
  id_no: {
    rules: [
      {required: true, errorMessage: '请填写证件号码'}
    ]
  }
})
const idTypes = ref([])
const isAg = ref([])
const isEdit = computed(() => {
  return model.value.id > 0
})

watch(show, (e) => emit('close'))

function onIsDefault(e) {
  model.value.is_default = e.detail.value ? Enable : Disable
}

function onSubmit() {
  form.value.validate().then((res) => {
    if (!isAg.value.length > 0) {
      showToast('请阅读并同意协议')
      return
    }

    const data = {...model.value}

    peopleSave(data).then(() => {
      showToast('保存成功')
      emit('close')
    })
  })
}

function onRemove() {
  showConfirm('删除出行人', '您确定要删除该出行人吗？').then(() => {
    peopleRemove(model.value.id).then(() => {
      showToast('删除成功').then(() => {
        emit('remove', model.value.id)
      })
    })
  })
}

onMounted(() => {
  peopleOptions().then(({data}) => {
    idTypes.value = data.id_types.map((item) => {
      item.text = item.label
      return item
    })
  })
})
</script>

<style lang="scss" scoped>
@import '../../../styles/_define.scss';
@import '../../../styles/_mix.scss';

.default {
  @include center();
  justify-content: space-between;
  background: white;
  border-radius: $border-radius-v2;
  padding: $padding-page;
}

.protocol {

  .txt {
    margin-top: $padding-small;
    color: $font-color-gray;
    font-size: $fontsize-mini;
  }

}

.remove {
  @include center();

  > view {
    @include center(column);
    gap: $padding-v2;

    .iconfont {
      color: $plan-color-3;
    }
  }
}
</style>
