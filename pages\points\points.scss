@import "../../styles/_define.scss";
@import "../../styles/_mix.scss";

/* 隐藏全局滚动条 */
:deep(page) {
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

.points-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0)),
    linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0)),
    linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1)) !important;

  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.tab-container {
  display: flex;
  background-color: transparent;
  padding-left: 60rpx;
  width: 100%;
  flex: 1;
  justify-content: center;
  gap: 40rpx;
}

.tab-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  cursor: pointer;
}

.tab-icon {
  width: 40rpx;
  height: 40rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #666;

  .tab-item.active & {
    color: #333;
    font-weight: bold;
  }
}

.tab-indicator {
  position: absolute;
  bottom: 12rpx;
  left: 0rpx;
  right: 0rpx;
  height: 20rpx;
  background-color: #FFE47C;
  border-radius: 10rpx;
  z-index: -1;
}

.swiper-container {
  flex: 1;
  width: 100%;
  /* 注意: 这里使用了 CSS 变量，在组件中通过 style 绑定 */
  box-sizing: border-box;
}

.swiper-item {
  height: auto; /* 改为 auto，让内容自然撑开 */
  min-height: 100%; /* 保证最小高度 */
}

// 确认购买弹窗样式
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.confirm-modal {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.modal-content {
  .agreement-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;

    .checkbox-wrapper {
      margin-right: 16rpx;
    }

    .checkbox {
      width: 32rpx;
      height: 32rpx;
      border: 2rpx solid #1890FF;
      border-radius: 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: white;
      position: relative;

      &.checked::after {
        content: '';
        position: absolute;
        top: 2rpx;
        left: 2rpx;
        right: 2rpx;
        bottom: 2rpx;
        background: #1890FF;
        border-radius: 2rpx;
      }
    }

    .agreement-text {
      font-size: 28rpx;
      color: #333333;

      .service-link {
        color: #1890FF;
      }
    }
  }

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    background: #1890FF;
    color: white;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &.disabled {
      background: #CCCCCC;
      color: #999999;
    }
  }
}
