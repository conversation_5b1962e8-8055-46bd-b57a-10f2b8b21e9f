<template>
	<view class="plans">
		<view class="list">
			<PlanCard @remove="() => {
				list.splice(index, 1)
			}" :data="item" v-for="(item, index) in list" :key="item.id">
			</PlanCard>
		</view>
		<uni-load-more @clickLoadMore="handleMore" :content-text="loadMoreText" :status="loadMoreState"></uni-load-more>
	</view>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app'
import { userPlans } from '../../../api/user';
import { navTo } from '@/utils';
import PlanCard from './components/PlanCard.vue';

const total = ref(0)
const pageSize = 20
let page = 1
const list = ref([])
const loadMoreState = ref('more')
const loadMoreText = {
	contentdown: '点击加载更多',
	contentrefresh: '正在加载',
	contentnomore: '没有更多数据了'
}

function hasMore(total) {
	return Math.ceil(total / pageSize) > page
}

function getList() {
	const params = {
		page,
		page_size: pageSize,
	}

	userPlans(params).then(res => {
		const {
			data
		} = res

		total.value = data.total
		list.value.push(...data.list)

		if (hasMore(data.total)) {
			loadMoreState.value = 'more'
		} else {
			loadMoreState.value = 'noMore'
		}
	})
}

function handleMore() {
	if (!hasMore(total.value)) {
		return
	}

	page += 1
	getList()
}

onLoad(() => {
	getList()
})
</script>

<style lang="scss" scoped>
@import 'plans.scss';
</style>