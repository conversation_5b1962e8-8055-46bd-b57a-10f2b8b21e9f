@import '../../styles/_mix.scss';
@import '../../styles/_define.scss';

.container {
  background: #F7F7F9;
  min-height: 100vh;
}

swiper {
  height: 420rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

:deep(.tabs) {
  padding-left: 28rpx;
  padding-right: 28rpx;
  background: white;

  justify-content: flex-start !important;
  gap: 47rpx;

  .active {
    color: #333333 !important;
    font-weight: bold !important;

    .line {
      height: 8rpx;
      background: #1890FF !important;
      width: 100%;
    }
  }
}

:deep(.yj-waterfall) {
  gap: 22rpx;
  margin: 20rpx $padding-page;
}

.tuan-item {
  border-bottom-left-radius: $border-radius-v2;
  border-bottom-right-radius: $border-radius-v2;
  background: white;
  margin-bottom: 20rpx;

  > view {
    &:last-child {
      padding: 20rpx;
    }
  }

  .name {
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .price {
    margin-top: 22rpx;
    color: #FF4D4F;

    text {
      font-size: 20rpx;
    }
  }

  .tag {
    font-size: 24rpx;
    margin-right: 4rpx;
    padding: 2rpx 16rpx;

    &:last-child {
      margin-right: 0;
    }

    &.hot {
      color: white;
      background: linear-gradient(270deg, #FF9F05 0%, #FF4D4F 100%);
    }
  }

  image {
    width: 336rpx;
    border-top-left-radius: $border-radius-v2;
    border-top-right-radius: $border-radius-v2;
  }
}