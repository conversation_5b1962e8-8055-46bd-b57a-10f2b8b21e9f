@import "../../styles/_define.scss";
@import "../../styles/_mix.scss";

// 页面专用变量
$detail-border-radius: 20rpx;
$detail-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
$drag-handle-height: 60rpx;
$drag-indicator-width: 80rpx;
$drag-indicator-height: 8rpx;
$timeline-item-margin: 30rpx;
$timeline-line-width: 4rpx;
$timeline-circle-size: 32rpx;
$timeline-dot-size: 20rpx;
$menu-item-size: 114rpx;
$action-btn-height: 72rpx;
$bottom-bar-padding: 18rpx 30rpx 30rpx 30rpx;

// 通用菜单项样式 mixin
@mixin menu-item-base {
  width: $menu-item-size;
  height: $menu-item-size;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 10rpx;
  border-radius: 20rpx;
  background: white;
  cursor: pointer;

  .image_top {
    width: 40rpx;
    height: 40rpx;
  }

  text {
    font-size: 20rpx;
    color: #333;
  }

  &.more_text text {
    font-size: 20rpx !important;
  }

  &.add_more_day text {
    font-size: 18rpx !important;
  }

  &:hover {
    background: #e8f4ff;
  }
}

// 时间线样式 mixin
@mixin timeline-line($color) {
  &::after,
  .timeline-item-card::before {
    background: $color;
  }
}

@mixin timeline-item-background($color) {
  .timeline-item-card {
    background: rgba($color, 0.05);

    &.type-text {
      background: #F7F7F9;
    }

    .type-icon {
      background: rgba($color, 0.4);
    }

    &.type-text .type-icon {
      background: #D3D5D9;
    }
  }
}

// 三点菜单样式 mixin
@mixin three-dots-menu($direction: horizontal) {
  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background: #333;
    transition: background-color $transition-fast;
  }

  &::before {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  @if $direction == horizontal {
    &::after {
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) translateX(-16rpx);
      box-shadow: 32rpx 0 0 #333;
    }
  } @else {
    &::after {
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) translateY(-16rpx);
      box-shadow: 0 32rpx 0 #333;
    }
  }

  &.active {
    &::before,
    &::after {
      background: #1890ff;
    }

    @if $direction == horizontal {
      &::after {
        box-shadow: 32rpx 0 0 #1890ff;
      }
    } @else {
      &::after {
        box-shadow: 0 32rpx 0 #1890ff;
      }
    }
  }
}

// 通用按钮样式 mixin
@mixin action-button-base {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 46rpx;
  cursor: pointer;
  transition: all $transition-fast;

  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

@mixin primary-button {
  @include action-button-base;
  background: #1890ff;
  color: white;

  &:hover {
    background: #40a9ff;
  }
}

@mixin outline-button {
  @include action-button-base;
  border: 2rpx solid #1890ff;
  background: white;
  color: #1890ff;

  &:hover {
    background: rgba(24, 144, 255, 0.05);
  }
}

// 过渡动画变量
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

.container {
  background: transparent;
  min-height: 100vh;
  height: 100vh;
  position: relative;
  width: 100%;
  overflow: hidden; // 防止整体滚动，我们只希望详情内容可以滚动
  will-change: transform; // 优化性能
}

// 底部操作栏
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  display: flex;
  gap: 22rpx;
  padding: 18rpx 30rpx 30rpx 30rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0rpx -4rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);

  .action-btn {
    flex: 1;
    padding: 18rpx 30rpx;

    .btn-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 16rpx;
    }

    .btn-text {
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .ai-optimize-btn {
    @include outline-button;
  }

  .save-btn {
    @include primary-button;
  }
}

.map-container {
  width: 100%;
  height: 100vh; // 填充整个屏幕高度
  position: absolute; // 绝对定位
  top: 0;
  left: 0;
  z-index: 1; // 设置为较低层级
  overflow: hidden;

  map {
    width: 100%;
    height: 100%;
  }
}

.map-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 252rpx;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 245, 1) 0%,
    rgba(238, 238, 238, 0) 100%
  );
  pointer-events: none;
  z-index: 10;
}

.detail-container {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0; // 左上和右上圆角
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  position: absolute; // 绝对定位
  left: 0;
  bottom: 0;
  z-index: 10; // 设置为较高层级
  height: 85vh; // 最大高度85%，与js中的计算保持一致

  // 详情区域状态由JS动态控制transform，无需额外CSS样式

  // 拖拽手柄样式
  .drag-handle {
    width: 100%;
    height: 60rpx; // 增大高度，提供更大的交互面积
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: grab;
    border-radius: 20rpx 20rpx 0 0;
    touch-action: none;
    -webkit-user-select: none;
    user-select: none;
    z-index: 10;
    position: relative; // 添加定位属性
    background-color: rgba(255, 255, 255, 0.95); // 添加轻微背景色

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    .drag-indicator {
      width: 80rpx; // 增大指示器宽度
      height: 8rpx; // 增大指示器高度
      background-color: #cccccc; // 更深的颜色使其更明显
      border-radius: 4rpx;
      margin-top: 10rpx;
      margin-bottom: 10rpx;
      position: relative;
      z-index: 2;
    }

    &:active {
      cursor: grabbing;

      .drag-indicator {
        background-color: #999999; // 拖动时颜色变深
        width: 100rpx; // 拖动时增大宽度，提供视觉反馈
        transition: all $transition-fast;
      }
    }
  }

  .detail-content {
    padding: 30rpx;
    padding-top: 0; // 减少顶部内边距，因为有了拖拽手柄
    padding-bottom: 130rpx; // 底部留出空间，避免被底部操作栏遮挡
    height: 100%; // 填充父容器高度
    overflow-y: hidden; // 默认隐藏溢出内容
    -webkit-overflow-scrolling: touch; // 在iOS上启用平滑滚动
    box-sizing: border-box; // 确保内边距包含在高度内
    transform: translateZ(0); // 强制GPU加速
    transition: overflow $transition-normal; // 为滚动状态变化添加过渡效果

    // 滚动激活状态
    &.scroll-active {
      overflow-y: auto; // 允许垂直滚动
      backface-visibility: hidden; // 减少页面闪烁
      perspective: 1000; // 使用硬件加速
    }

    // 隐藏滚动条但保留功能
    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }

    // 针对 uni-app scroll-view 组件的滚动条隐藏
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    // 确保在所有平台都隐藏滚动条
    &::-webkit-scrollbar-track {
      display: none;
    }

    &::-webkit-scrollbar-thumb {
      display: none;
    }

    .top-container {
      display: flex;
      justify-content: space-between; /* 核心：两端对齐 */
      align-items: center; /* 推荐：让两个元素在交叉轴上（垂直方向）居中 */
      .left-container {
        display: flex;
        flex-direction: column;

        .title-container {
          display: flex;
          align-items: center;
          gap: 16rpx;
          margin-bottom: 10rpx;

          .title {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            max-width: 480rpx;
            font-size: 34rpx;
            font-weight: 600;
            color: #333;
          }

          .edit-icon {
            width: 40rpx;
            height: 40rpx;
            cursor: pointer;
            transition: opacity $transition-fast;

            &:active {
              opacity: 0.6;
            }
          }
        }

        .date {
          font-size: 28rpx;
          color: #333;
        }
      }
      .right-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        .price {
          font-size: 28rpx;
          color: #1890ff;
          margin-bottom: 10rpx;
        }
        .price-desc {
          font-size: 20rpx;
          color: #999;
        }
      }
    }
    .middle-container {
      margin-top: 30rpx;
      padding: 10rpx 30rpx 10rpx 30rpx;
      background-color: #f5faff;
      border-radius: 20rpx 20rpx 0 0;
      .first-container {
        margin-top: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .m-left-container {
          display: flex;
          .image {
            width: 40rpx;
            height: 40rpx;
          }
          .title {
            font-size: 28rpx;
            margin-left: 8rpx;
          }
        }
        .right-container {
          .text {
            font-size: 28rpx;
          }
        }
      }
    }
    .last-container {
      display: flex;
      padding: 20rpx 20rpx 30rpx 30rpx;
      justify-content: space-between;
      align-items: center;
      background-color: #e7f4ff;
      border-radius: 0 0 20rpx 20rpx;
      .l-text {
        font-size: 28rpx;
        color: #1890ff;
      }
      .r-view {
        display: flex;
        align-items: center;
        .r-text {
          font-size: 28rpx;
          color: #1890ff;
        }
        .r-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
    .list-container {
      margin-top: 40rpx;

      // 天数tab栏样式
      .days-tab-container {
        position: sticky;
        top: -1rpx;
        z-index: 100;
        background: white;
        padding: 16rpx 0;
        margin-bottom: 30rpx;

        .days-tab-wrapper {
          width: 100%;
          overflow: hidden;

          .days-tab-scroll {
            display: flex;
            align-items: center;
            gap: 16rpx;
            padding: 0 30rpx;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }

            .day-tab {
              position: relative;
              width: 120rpx;
              height: 64rpx;
              border-radius: 20rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              cursor: pointer;
              transition: all $transition-normal;

              .day-tab-content {
                position: relative;
                z-index: 2;

                .day-text {
                  font-size: 24rpx;
                  color: #666;
                  font-weight: 500;
                }
              }

              // 选中状态
              &.active {
                background-color: #e7f4ff;

                .day-text {
                  color: #1890ff;
                  font-weight: 600;
                }

                .day-tab-indicator {
                  position: absolute;
                  bottom: 8rpx;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 104rpx;
                  height: 28rpx;
                  background-color: #faca14;
                  border-radius: 14rpx;
                  z-index: 1;
                }
              }

              // 悬停效果
              &:hover:not(.active) {
                background-color: #f8f9fa;
              }
            }

            .add-day-tab {
              width: 40rpx;
              height: 40rpx;
              border-radius: 20rpx;
              // background-color: #f5f5f5;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              cursor: pointer;
              transition: all $transition-normal;

              .iconfont {
                font-size: 28rpx;
                color: #999;
              }

              &:hover {
                background-color: #e8e8e8;

                .iconfont {
                  color: #666;
                }
              }
            }
          }
        }
      }

      // 重写data区域样式
      .data {
        .section-list {
          .section-item {
            margin-bottom: 60rpx;
            position: relative;

            // 除第一个外，添加上方连接线
            &:not(:first-child) {
              &::before {
                content: "";
                position: absolute;
                left: 14rpx;
                top: -60rpx;
                width: 4rpx;
                height: 60rpx;
                z-index: 10;
              }
            }

            // 统一的主时间线
            &::after {
              content: "";
              position: absolute;
              left: 14rpx;
              top: 16rpx;
              bottom: -60rpx;
              width: 4rpx;
              z-index: 1;
            }

            // 菜单激活状态的背景 - 直接设置容器背景
            &.menu-active {
              .section-header {
                background: #f7f7f9;
                margin: 0 -20rpx 0 -20rpx;
                padding: 20rpx;
                border-radius: 16rpx 16rpx 0 0;
                margin-bottom: 0;
              }

              // 调整时间线位置以适应padding - 主要是top位置
              &::after {
                top: 36rpx; // 16rpx + 20rpx padding
              }

              // 调整连接线位置
              &:not(:first-child)::before {
                top: -40rpx; // -60rpx + 20rpx padding
              }
            }

            // 天数标题区域
            .section-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 30rpx;
              position: relative;

              .day-indicator {
                display: flex;
                align-items: center;
                gap: 20rpx;
                position: relative;

                .day-circle {
                  width: 32rpx;
                  height: 32rpx;
                  border-radius: 50%;
                  color: white;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 24rpx;
                  font-weight: 600;
                  position: absolute;
                  left: 0;
                  z-index: 3;
                }

                .day-title {
                  font-size: 28rpx;
                  font-weight: 500;
                  color: #333;
                  margin-left: 52rpx;
                }

                .day-subtitle {
                  font-size: 24rpx;
                  color: #999;
                  margin-left: 10rpx;
                }
              }

              .day-menu-btn {
                width: 60rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                @include three-dots-menu(horizontal);
              }
            }

            // 天数操作菜单
            .day-menu-popup {
              background: #f7f7f9;
              border-radius: 0 0 16rpx 16rpx;
              padding: 20rpx;
              padding-left: 50rpx;
              margin: 0 -20rpx 30rpx -20rpx;
              display: flex;
              justify-content: center;
              gap: 20rpx;
              position: relative;

              .menu-item {
                @include menu-item-base;

                &.delete {
                  .iconfont {
                    color: #ff4d4f;
                  }
                }
              }
            }

            // 时间线容器
            .timeline-container {
              position: relative;

              // 时间线项目卡片
              .timeline-item-card {
                position: relative;
                margin-bottom: 30rpx;
                margin-left: 40rpx;
                background: white;
                border-radius: 16rpx;
                
                overflow: visible; // 改为visible让圆点显示
                z-index: 2;

                // 时间线圆点
                .timeline-dot {
                  position: absolute;
                  left: -34rpx; // 减少偏移，正好在时间线上
                  top: 50%; // 垂直居中
                  transform: translateY(-50%); // 精确居中
                  width: 20rpx;
                  height: 20rpx;
                  border-radius: 50%;
                  background: #1890ff; // 改回默认蓝色
                  box-shadow: 0 0 0 3rpx white, 0rpx 2rpx 8rpx 0rpx rgba(0, 0, 0, 0.15);
                  z-index: 999;
                  display: block !important;
                }

                // 最后一个item，截断时间线
                &:last-child::after {
                  content: "";
                  position: absolute;
                  left: -20rpx;
                  top: 52rpx;
                  bottom: -30rpx;
                  width: 8rpx;
                  background: white;
                  z-index: 2;
                }

                // 类型图标
                .type-icon {
                  position: absolute;
                  top: 0%;
                  right: 0rpx;
                  width: 40rpx;
                  height: 40rpx;
                  z-index: 3;
                  border-radius: 0rpx 16rpx 0rpx 16rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  image {
                    width: 32rpx;
                    height: 32rpx;
                  }
                }

                // 内容区域
                .item-content {
                  padding: 30rpx;
                  padding-right: 80rpx;

                  // 交通内容
                  .transport-content {
                    display: flex;
                    align-items: center;
                    gap: 20rpx;

                    .transport-from,
                    .transport-to {
                      display: flex;
                      flex-direction: column;
                      gap: 8rpx;

                      .zone-name {
                        font-size: 28rpx;
                        font-weight: 600;
                        color: #333;
                      }

                      .station {
                        font-size: 24rpx;
                        color: #999;
                      }
                    }

                    .transport-arrow {
                      width: 80rpx;
                      height: 20rpx;
                    }
                  }

                  // 普通内容
                  .item-header {
                    display: flex;
                    gap: 20rpx;
                    

                    .item-pic {
                      width: 136rpx;
                      height: 136rpx;
                      border-radius: 12rpx;
                      overflow: hidden;
                      flex-shrink: 0;

                      image {
                        width: 100%;
                        height: 100%;
                      }
                    }

                    .item-info {
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      gap: 12rpx;

                      .item-title {
                        font-size: 28rpx;
                        font-weight: 600;
                        color: #333;
                        line-height: 1.4;
                      }

                      .item-desc {
                        font-size: 28rpx;
                        color: #333;
                        line-height: 1.5;
                        overflow: hidden;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                      }

                      .price-info {
                        display: flex;
                        align-items: center;
                        gap: 8rpx;

                        .price-label {
                          font-size: 28rpx;
                          color: #333;
                        }

                        .price {
                          font-size: 28rpx;
                          // font-weight: 600;
                          // color: #1890ff;
                        }
                      }
                    }
                  }

                  // 标签
                  .item-tags {
                    display: flex;
                    gap: 12rpx;
                    flex-wrap: wrap;

                    .tag {
                      padding: 0rpx 16rpx;
                      border-radius: 8rpx;
                      font-size: 20rpx;

                      &.tag-blue {
                        background: #e7f4ff;
                        color: #1890ff;
                      }

                      &.tag-orange {
                        background: #fff2e6;
                        color: #fa8c16;
                      }

                      &.tag-purple {
                        background: #f4e6ff;
                        color: #722ed1;
                      }
                    }
                  }
                }

                // 操作按钮
                .item-action-btn {
                  position: absolute;
                  top: 100rpx;
                  right: 10rpx;
                  width: 8rpx;
                  height: 40rpx;
                  display: flex;
                  padding: 0 20rpx;
                  align-items: center;
                  justify-content: center;
                  transform: translateY(-50%);
                  z-index: 999;

                  @include three-dots-menu(vertical);
                }

                // 项目操作菜单
                .item-menu-popup {
                  padding: 20rpx;
                  margin: 20rpx 0rpx;
                  display: flex;
                  gap: 15rpx;
                  justify-content: center;

                  .menu-item {
                    @include menu-item-base;
                    padding: 15rpx 8rpx;
                    justify-content: center;
                    border-radius: 8rpx;
                  }
                }
              }

              // 空状态
              .empty-actions {
                margin-left: 40rpx;

                .add-item-card {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 20rpx;
                  padding: 60rpx 30rpx;
                  background: #f8f9fa;
                  border-radius: 16rpx;
                  border: 2rpx dashed #d9d9d9;
                  cursor: pointer;

                  .iconfont {
                    font-size: 48rpx;
                    color: #999;
                  }

                  text:not(.iconfont) {
                    font-size: 28rpx;
                    color: #666;
                  }

                  &:hover {
                    border-color: #1890ff;
                    background: #e7f4ff;

                    .iconfont {
                      color: #1890ff;
                    }
                  }
                }
              }

              // 时间线底部添加按钮
              .timeline-add-button {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 20rpx;
                margin: 18rpx 0rpx 0rpx 40rpx;
                padding: 18rpx 0rpx;
                background: white;
                border-radius: 16rpx;
                border: 2rpx solid;
                cursor: pointer;
                transition: opacity $transition-fast;

                .add-icon {
                  width: 40rpx;
                  height: 40rpx;
                }
                .iconfont {
                  font-size: 26rpx;
                  // font-weight: 500;
                }
                .add-text {
                  font-size: 28rpx;
                  font-weight: 500;
                }

                &:active {
                  opacity: 0.8;
                }
              }
            }

            // 不同天数的颜色样式
            &.day-0 {
              @include timeline-line(#1890ff);
              @include timeline-item-background(#1890ff);
            }

            &.day-1 {
              @include timeline-line(#52c41a);
              @include timeline-item-background(#52c41a);
              &::before { background: #1890ff; }
            }

            &.day-2 {
              @include timeline-line(#fa8c16);
              @include timeline-item-background(#fa8c16);
              &::before { background: #52c41a; }
            }

            &.day-3 {
              @include timeline-line(#722ed1);
              @include timeline-item-background(#722ed1);
              &::before { background: #fa8c16; }
            }

            &.day-4 {
              @include timeline-line(#eb2f96);
              @include timeline-item-background(#eb2f96);
              &::before { background: #722ed1; }
            }

            &.day-5 {
              @include timeline-line(#13c2c2);
              @include timeline-item-background(#13c2c2);
              &::before { background: #eb2f96; }
            }

            &.day-6 {
              @include timeline-line(#f5222d);
              @include timeline-item-background(#f5222d);
              &::before { background: #13c2c2; }
            }

            &.day-7 {
              @include timeline-line(#faad14);
              @include timeline-item-background(#faad14);
              &::before { background: #f5222d; }
            }

            // 循环：第9天（day-0）的连接线使用第8天（day-7）的颜色
            &.day-0:not(:first-child)::before {
              background: #faad14;
            }
          }
        }
      }
    }
  }
}

// 圆点颜色样式（独立定义，确保能够匹配）
.timeline-dot {
  &.dot-day-0 { background: #1890ff !important; }
  &.dot-day-1 { background: #52c41a !important; }
  &.dot-day-2 { background: #fa8c16 !important; }
  &.dot-day-3 { background: #722ed1 !important; }
  &.dot-day-4 { background: #eb2f96 !important; }
  &.dot-day-5 { background: #13c2c2 !important; }
  &.dot-day-6 { background: #f5222d !important; }
  &.dot-day-7 { background: #faad14 !important; }
}

// 添加项目弹窗样式
.add-item-popup-content {
  padding: 30rpx;
  background: white;
  border-radius: $detail-border-radius $detail-border-radius 0 0;

  .add-item-option {
    display: flex;
    align-items: center;
    padding: 30rpx 20rpx;
    margin-bottom: 20rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    cursor: pointer;
    transition: all $transition-fast;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }

    .option-icon {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890ff;
      border-radius: 50%;
      margin-right: 20rpx;

      .iconfont {
        font-size: 32rpx;
        color: white;
      }
    }

    .option-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .option-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .option-desc {
        font-size: 24rpx;
        color: #666;
      }
    }

    .option-arrow {
      .iconfont {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

// 确保添加项目弹窗在底部常驻浮层之上
:deep(.add-item-popup) {
  .uni-popup {
    z-index: 1500 !important;
  }

  .uni-popup__wrapper {
    z-index: 1500 !important;
  }

  .my-info-popup2 {
    z-index: 1500 !important;
  }
}
