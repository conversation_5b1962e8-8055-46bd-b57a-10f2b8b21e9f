<template>
  <view class="refund-order-list">
    <!-- 列表容器 -->
    <view class="list">
      <!-- 订单项循环 -->
      <view v-for="item in list" :key="item.refund_order_id" class="order-item" @tap="handleOrderItemClick(item)">
        <!-- 订单头部 -->
        <view class="header">
          <view class="left">
            <text :class="{
              'icon-chuang': item.order_type === 'hotel',
              'icon-jingdian': item.order_type === 'ticket',
              'icon-huowudui': ['travel', 'tuan'].includes(item.order_type)
            }" class="iconfont"></text>
            {{ getOrderTypeText(item.order_type) }}
          </view>
          <view class="right">
            <text>{{ formatTime(item.refund_created_at, 'YYYY-M-D H:mm') }}</text>
            <text :class="`order-state-${item.state}`">{{ item.state_text }}</text>
          </view>
        </view>
        <!-- 订单标题 -->
        <view class="title" @tap.stop="navTo('pages/details/details', { plan_id: item.plan_id })"
              v-if="item.name.length && item.order_type === OrderTypeTravel">{{
            `${item.name} >`
          }}
        </view>

        <!-- 订单详情 -->
        <view class="details">
          <!-- 最多显示三个子项 -->
          <view v-for="(detail, index) in item.details.slice(0, 3)" :key="detail.order_detail_id" class="detail">
            <image :src="detail.pic" class="left" mode="aspectFill"></image>
            <view class="right">
              <view>
                <view class="name">{{ detail.product_name }}</view>
                <view class="sku_name">{{ detail.sku_name }}</view>
                <view class="date-info">
                  <!-- 酒店类型 -->
                  <template v-if="detail.order_sub_type === 3">
                    <view class="hotel-date">
                      <view class="date-range">
                        <view class="date-item">
                          <view class="weekday">{{ dayjs.unix(detail.date_start).format('ddd') }}</view>
                          <view class="date">{{ formatTime(detail.date_start, 'M月D日') }}</view>
                        </view>
                        <text class="separator">—</text>
                        <view class="date-item">
                          <view class="weekday">{{ dayjs.unix(detail.date_end).format('ddd') }}</view>
                          <view class="date">{{ formatTime(detail.date_end, 'M月D日') }}</view>
                        </view>
                      </view>
                    </view>
                  </template>
                  <!-- 门票类型 -->
                  <template v-else-if="detail.order_sub_type === 2">
                    <view class="ticket-date">
                      <view>{{ formatTime(detail.date_start) }} {{ dayjs.unix(detail.date_start).format('ddd') }}</view>
                    </view>
                  </template>
                </view>
              </view>
            </view>
          </view>
          <!-- 如果超过三个子项，显示更多提示 -->
          <view v-if="item.details.length > 3" class="more-items">
            还有 {{ item.details.length - 3 }} 个商品
          </view>
        </view>

        <!-- 订单底部 -->
        <view class="footer">
          <view class="left">
            <view class="price-info">
              <view class="total-price">总价：
                <text style="color: #333333; font-weight: 400;">{{
                    getTotalPreRefundAmount(item)
                  }}
                </text>
              </view>
              <view class="refund-amount">退款金额：
                <text style="color: #1890FF; font-weight: 400;">{{
                    getTotalRefundAmount(item)
                  }}
                </text>
              </view>
            </view>
          </view>
          <view class="right">
            <view class="btn detail-btn" @tap.stop="handleOrderItemClick(item)">查看详情</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <uni-load-more :status="loadMoreStatus"></uni-load-more>
  </view>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {onReachBottom} from '@dcloudio/uni-app'
import {getRefundOrderList} from '@/api/order.js'
import {formatMoney, formatTime, navTo} from '@/utils/index.js'
import {OrderTypeTravel} from '@/utils/constmap'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 设置dayjs语言为中文
dayjs.locale('zh-cn')

// 数据状态
const list = ref([])
const total = ref(0)
let page = 1
const pageSize = 10
const loading = ref(false)

// 计算属性
const hasMore = computed(() => Math.ceil(total.value / pageSize) > page)
const loadMoreStatus = computed(() => {
  if (loading.value) return 'loading'
  return hasMore.value ? 'more' : 'noMore'
})

// 获取订单类型文本
function getOrderTypeText(type) {
  const typeMap = {
    'hotel': '酒店',
    'ticket': '门票',
    'travel': '旅行',
    'tuan': '套餐'
  }
  return typeMap[type] || '商品'
}

// 计算订单总价
function getTotalPreRefundAmount(item) {
  if (!item.details || !item.details.length) return formatMoney(0)

  const total = item.details.reduce((sum, detail) => {
    return sum + (detail.pre_refund_amount || 0)
  }, 0)

  return formatMoney(total)
}


// 计算订单总退款金额
function getTotalRefundAmount(item) {
  if (!item.details || !item.details.length) return '--'

  const total = item.details.reduce((sum, detail) => {
    return sum + (detail.refund_amount || 0)
  }, 0)

  return total === 0 ? '--' : formatMoney(total)
}

// 获取退款订单列表
function getList() {
  if (loading.value) return

  loading.value = true
  getRefundOrderList({
    page,
    page_size: pageSize
  })
      .then(res => {
        const {data} = res
        total.value = data.total
        list.value.push(...data.list)
      })
      .finally(() => {
        loading.value = false
      })
}

// 处理订单项点击
function handleOrderItemClick(item) {
  navTo('pages/orders/refund', {refund_order_id: item.refund_order_id})
}

// 滚动到底部加载更多
onReachBottom(() => {
  if (loading.value || !hasMore.value) return
  page++
  getList()
})

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import "refundOrderList";
</style>