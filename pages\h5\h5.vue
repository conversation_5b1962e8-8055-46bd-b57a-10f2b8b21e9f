<script setup>

import {onLoad} from "@dcloudio/uni-app";
import {ref} from "vue";
import {useUserStore} from "@/store/user";
import {deepToRaw, hideLoading, navBack, navTo, showLoading, showToast, getH5Url} from "@/utils";
import {isProductionEnvironment} from "@/utils/device";

const webviewUrl = ref('')
const userStore = useUserStore()
const isLoad = ref(false)

/**
 * 检查域名是否在白名单中
 * @param {string} targetUrl
 * @returns {boolean}
 */
function isWhitelistedDomain(targetUrl) {
  try {
    // 使用正则表达式提取域名，兼容小程序环境
    const hostnameRegex = /^(?:https?:\/\/)?([^\/]+)/i
    const match = targetUrl.match(hostnameRegex)

    if (!match) return false

    // 提取主域名部分
    let hostname = match[1]
    // 如果包含端口号，去除端口号
    hostname = hostname.split(':')[0]

    // 白名单域名列表
    const whitelistedDomains = ['h5.funfuntrip.cn']

    return whitelistedDomains.includes(hostname)
  } catch (error) {
    console.error('域名解析错误:', error)
    return false
  }
}

/**
 * 检查是否应该注入用户信息
 * @param {string} targetUrl
 * @returns {boolean}
 */
function shouldInjectUserInfo(targetUrl) {
  const isWhitelisted = isWhitelistedDomain(targetUrl)

  return !isProductionEnvironment() || isWhitelisted
}

/**
 * 添加token到URL
 * @param {string} originalUrl
 * @returns {string}
 */
function addTokenToUrl(originalUrl) {
  if (!userStore.isLogin || !shouldInjectUserInfo(originalUrl)) {
    console.log('用户未登录或URL不在白名单中，跳过token注入')
    return originalUrl
  }

  try {
    const token = userStore.token
    if (!token) {
      console.log('无有效token，跳过注入')
      return originalUrl
    }

    // 检查URL是否已有参数
    const separator = originalUrl.includes('?') ? '&' : '?'
    const urlWithToken = `${originalUrl}${separator}token=${encodeURIComponent(token)}`

    console.log('已将token添加到URL')
    return urlWithToken
  } catch (error) {
    console.error('添加token到URL失败:', error)
    return originalUrl
  }
}

/**
 * 处理webview消息
 * @param {Object} event
 */
function handleWebviewMessage(event) {
  console.log('收到webview消息:', event)

  // 安全获取消息数组
  const messages = Array.isArray(event.detail?.data) ? event.detail.data : []

  // 处理每条消息
  messages.forEach(message => {
    if (message?.type === 'download') {
      const {url} = message
      if (!url) {
        console.error('下载URL不能为空')
        return
      }

      showLoading('正在下载文件')

      // 下载文件
      uni.downloadFile({
        url,
        success: (downloadResult) => {
          if (downloadResult.statusCode === 200) {
            // 保存到系统相册
            uni.saveImageToPhotosAlbum({
              filePath: downloadResult.tempFilePath,
              success: () => {
                hideLoading()
                showToast('图片已成功保存到相册')
                if (message.sub_type === 'poster') {
                  const posterUrl = getH5Url('#/activity/poster')
                  navTo(posterUrl)
                }
              },
              fail: (err) => {
                hideLoading()
                console.error('保存文件失败:', err)
                showToast('保存图片到相册失败')
              }
            })
          }
        },
        fail: (err) => {
          hideLoading()
          console.error('下载文件失败:', err)
          showToast('下载文件失败')
        }
      })
    }
  })
}

onLoad(query => {
  const originalUrl = decodeURIComponent(query.url || '')
  // 添加token到URL
  webviewUrl.value = addTokenToUrl(originalUrl)
  isLoad.value = true

  console.log('h5.vue onLoad:', query)
})

</script>

<template>
  <web-view v-if="isLoad" :src="webviewUrl" @message="handleWebviewMessage"></web-view>
</template>

<style lang="scss" scoped></style>
