import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
    createSSRApp
} from 'vue'
import {
    createPinia
} from 'pinia'
// #endif

// #ifdef MP-WEIXIN
import uma from 'umtrack-wx';
uma.init({
    appKey:import.meta.env.VITE_UMENG_KEY,
    useOpenid:false,
    autoGetOpenid:false,
    debug:true
});
// #endif

export function createApp() {
    const app = createSSRApp(App)
    app.use(createPinia())
    
    // #ifdef MP-WEIXIN
    app.use(uma)
    // #endif
    
    return {
        app
    }
}
