<template>
  <uni-popup ref="popup" :mask-click="maskClick" type="bottom" @change="handleChange">
    <!-- 额外的遮罩层事件拦截，仅阻止滑动穿透，不阻止点击事件 -->
    <view v-if="isOpen" class="mask-event-blocker" @touchmove.prevent @wheel.prevent></view>

    <view class="import-trip-popup" @touchmove.prevent @wheel.prevent>
      <view class="close">
        <text class="iconfont icon-guanbi" @tap="onClose"></text>
      </view>
      <view class="container">
        <view class="header">
          <view class="icon-container">
            <text class="iconfont icon-lianjie"></text>
          </view>
          <text class="title">文本或链接识别</text>
        </view>
        <view class="content">
          <textarea v-model="inputText" class="input-area"
                    placeholder="粘贴站外攻略链接到这里，即可复制该行程。支持小红书笔记链接、微信公众号链接。"
                    placeholder-style="color: #B7B9BD;" :maxlength="-1" auto-height :cursor-spacing="160"
                    :show-confirm-bar="false"/>
        </view>
        <view class="footer">
          <MyButton @tap="onSubmit" size="small" type="primary" :disable="!hasInput">开始识别</MyButton>
          <!-- <button class="submit-btn" :class="{ 'active': hasInput }" @tap="onSubmit">开始识别</button> -->
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import {ref, onMounted, onBeforeUnmount, computed} from 'vue';
import MyButton from '../MyButton/MyButton.vue';

const popup = ref(null);
const inputText = ref('');
const isOpen = ref(false);

const hasInput = computed(() => {
  return inputText.value.trim().length > 0;
});

const emit = defineEmits(['close', 'submit']);
const props = defineProps({
  maskClick: {
    type: Boolean,
    default: true
  }
});

// 处理滚动穿透
function handleScroll(e) {
  // 只处理滚动事件，不阻止点击
  if (e.type === 'touchmove' || e.type === 'wheel') {
    e.preventDefault();
    e.stopPropagation();
  }
  return false;
}

// 禁止页面滚动
function disableScroll() {
  // #ifdef H5
  try {
    document.body.style.overflow = 'hidden';
    document.addEventListener('touchmove', handleScroll, {
      passive: false
    });
  } catch (e) {
    console.error('Failed to disable scroll:', e);
  }
  // #endif
}

// 恢复页面滚动
function enableScroll() {
  // #ifdef H5
  try {
    document.body.style.overflow = '';
    document.removeEventListener('touchmove', handleScroll);
  } catch (e) {
    console.error('Failed to enable scroll:', e);
  }
  // #endif
}

function handleChange(e) {
  isOpen.value = e.show;
  if (!e.show) {
    emit('close');
    enableScroll();
  } else {
    disableScroll();
  }
}

function onClose() {
  popup.value.close();
}

function onSubmit() {
  if (inputText.value.trim()) {
    emit('submit', inputText.value);
    inputText.value = '';
    onClose();
  } else {
    uni.showToast({
      title: '请输入文本或链接',
      icon: 'none'
    });
  }
}

onMounted(() => {
  popup.value.open();
});

// 组件销毁时确保恢复滚动
onBeforeUnmount(() => {
  if (isOpen.value) {
    enableScroll();
  }
});
</script>

<style lang="scss">
@use '../../styles/_define.scss';
@use '../../styles/_mix.scss';

/* 全局覆盖uni-popup样式，防止滑动穿透 */
:deep(.uni-popup) {
  position: relative;
  z-index: 999;
}

:deep(.uni-popup .uni-transition[name="mask"]) {
  touch-action: none !important;
  pointer-events: auto !important;
}

:deep(.uni-popup .uni-transition) {
  position: fixed;
}

:deep(.uni-popup__wrapper) {
  touch-action: none !important;
  z-index: 1000 !important;
}

/* 额外的遮罩层事件拦截 */
.mask-event-blocker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 900;
  pointer-events: none;
}

.import-trip-popup {
  position: relative;
  z-index: 1000;

  .close {

    &,
    text {
      @include mix.center();
    }

    text {
      color: white;
      width: 48rpx;
      height: 48rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 50%;
      margin-bottom: 20rpx;
    }
  }

  .container {
    background: #FFFFFF;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    padding: 30rpx;

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .icon-container {
        margin-right: 10rpx;

        .iconfont {
          font-size: 40rpx;
          color: #1890FF;
        }
      }

      .title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
      }
    }

    .content {
      margin-bottom: 30rpx;

      .input-area {
        width: 100%;
        min-height: 200rpx;
        background: #F7F7F9;
        border-radius: 10rpx;
        padding: 20rpx;
        font-size: 28rpx;
        color: #333333;
        box-sizing: border-box;
      }
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 20rpx;
      margin-bottom: 16rpx;
      width: 100%; // 确保占满父容器

      .submit-btn {
        padding: 16rpx, 44rpx;
        background: #EDEEF0;
        color: white;
        font-size: 28rpx;
        border-radius: 36rpx;
        margin: 0; // 强制右对齐

        &.active {
          background: #1890FF;
        }
      }
    }
  }
}
</style>
