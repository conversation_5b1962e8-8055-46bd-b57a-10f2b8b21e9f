<script setup>
import { orderSubmit } from '@/api/order';
import { productDetail, productSkudates, } from '@/api/tuan.js';
import MyButton from '@/components/MyButton/MyButton.vue';
import { useGlobalStore } from '@/store/global';
import { useUserStore } from '@/store/user';
import { formatMoney, navBack, navTo, showModal, showToast } from '@/utils';
import { CustomerTypeAdult, CustomerTypeChild, GlobalTypePeople } from '@/utils/constmap';
import { isMobileFunc } from '@/utils/validators';
import { onLoad, onShow } from '@dcloudio/uni-app';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';

const globalStore = useGlobalStore()
const userStore = useUserStore()
const formData = ref({
  product_id: 0,
  sku_id: 0,
  num: 1,
  date: 0, //日期时间戳
  adults: [],
  children: [],
})
const sku = ref({
  name: '',
  price: 0,
  adult_num: 0,
  children_num: 0,
})
const detail = ref({
  days: 0,
  skus: [],
})
const contact = ref({
  name: '',
  phone: '',
})
const dates = computed(() => ({
  start: dayjs.unix(formData.value.date).format('YYYY-MM-DD'),
  end: dayjs.unix(formData.value.date).add(detail.value.days, 'day').format('YYYY-MM-DD'),
}))
const totalPrice = computed(() => {
  return sku.value.price > 0 ? (sku.value.price * formData.value.num) : 0
})
const formError = computed(() => {
  const form = formData.value
  const c = contact.value
  const nameFinder = (v) => (v.name == '')
  if (form.adults.find(nameFinder) || form.children.find(nameFinder)) {
    return '请填写旅客信息'
  }
  if (c.name == '') {
    return '请填写联系人信息'
  }
  if (!isMobileFunc(c.phone)) {
    return '联系人手机号不能为空'
  }
  return null
})

let currentPeople = null //当前选中跳转到出行人选择页的对象

onLoad(query => {
  Object.assign(formData.value, {
    product_id: query.product_id,
    sku_id: Number(query.sku_id),
    num: Number(query.num),
    date: Number(query.date),
  })

  productDetail(formData.value.product_id).then(({ data }) => {
    Object.assign(detail.value, data)
    let s = data.skus.find(v => v.id == formData.value.sku_id)
    if (!s) {
      showToast('数据错误:规格不存在')
      navBack()
      return
    }
    delete s.price //弃用字段要删除不然会影响团期接口内的赋值
    const num = formData.value.num
    Object.assign(sku.value, s)
    if (data.need_people) {
      Object.assign(formData.value, {
        adults: Array(num * s.adult_num).fill(0).map(() => ({ name: '', id_no: '', customer_type: CustomerTypeAdult })),
        children: Array(num * s.children_num).fill(0).map(() => ({
          name: '',
          id_no: '',
          customer_type: CustomerTypeChild
        })),
      })
    }
  })

  productSkudates({
    tuan_id: formData.value.product_id,
    start: formData.value.date,
    end: formData.value.date,
  }).then(({ data }) => {
    let target = dayjs.unix(formData.value.date)
    data.list.forEach(dateItem => {
      if (dayjs.unix(dateItem.date).isSame(target, 'day')) {
        dateItem.skus.forEach(skuPrice => {
          if (skuPrice.sku_id == formData.value.sku_id) {
            Object.assign(sku.value, skuPrice)
          }
        })
      }
    })
  })

})

onShow(() => {
  if (userStore.token === '') {
    navTo('pages/login/login')
    return
  }

  if (!currentPeople) {
    return
  }
  if (globalStore.data?.type === GlobalTypePeople && globalStore.data.data.length > 0) {
    let people = globalStore.data.data[0]
    Object.assign(currentPeople, {
      name: people.name,
      id_type: people.id_type,
      id_no: people.id_no,
      phone: people.phone,
    })
    currentPeople = null
    globalStore.clearData()
  }
})

const doSelectPeople = (who, required = '') => {
  currentPeople = who
  navTo('pages/peoples/peoples', {
    required,
    choose: 1,
  })
}

const doEmptyPeople = (who) => {
  Object.assign(who, {
    name: '',
    id_type: 0,
    id_no: '',
    phone: '',
  })
}

const doSubmit = async () => {
  if (formError.value) {
    showToast(formError.value)
    return
  }
  const form = formData.value

  const params = {
    product_type: 'tuan',
    order_type: 'tuan',
    product_id: form.product_id,
    date: form.date,
    skus: [{ id: form.sku_id, quantity: form.num }],
    name: contact.value.name,
    tel: contact.value.phone,
    peoples: [].concat(form.adults).concat(form.children),
  }
  params.skus = JSON.stringify(params.skus)
  params.peoples = JSON.stringify(params.peoples)

  const { data } = await orderSubmit(params)
  if (!data.need_pay) {
    await showModal('报名成功', '确认提交后，客服24小时内通过电话或短信通知您')
    navTo('pages/orders/orders', {}, true)
    return
  }

  navTo('pages/pay/pay', { order_id: data.order_id }, true)
}

</script>

<template>
  <view class="container">
    <view class="infos">
      <text class="title">{{ detail.name }}</text>
      <text class="normal">{{ dates.start }}出发</text>
      <text class="normal">{{ dates.end }}返回</text>
    </view>
    <view v-if="formData.adults.length || formData.children.length" class="icard">
      <view class="title">
        旅客信息
        <text class="tip">请添加{{ sku.adult_num }}成人{{ sku.children_num }}儿童</text>
      </view>
      <view class="peoples">
        <template v-for="(item, i) in formData.adults" :key="i">
          <view class="lines">
            <text class="room-num" @tap="doSelectPeople(item)">
              成人{{ i + 1 }}
              <text v-if="item.name != ''" class="name">{{ item.name }}</text>
              <text v-else class="placeholder">请输入旅客信息</text>
            </text>
            <text v-if="item.name != ''" class="iconfont icon-shanchu1" @tap="doEmptyPeople(item)" />
            <text class="iconfont icon-address-book" @tap="doSelectPeople(item)"></text>
          </view>
        </template>
        <template v-for="(item, i) in formData.children" :key="i">
          <view class="lines">
            <text class="room-num" @tap="doSelectPeople(item)">
              儿童{{ i + 1 }}
              <text v-if="item.name != ''" class="name">{{ item.name }}</text>
              <text v-else class="placeholder">请输入旅客信息</text>
            </text>
            <text v-if="item.name != ''" class="iconfont icon-shanchu1" @tap="doEmptyPeople(item)" />
            <text class="iconfont icon-address-book" @tap="doSelectPeople(item)"></text>
          </view>
        </template>
        <view class="warn">
          <text>*请务必填写与出行人证件一致的信息</text>
        </view>
      </view>
    </view>
    <view class="icard contact">
      <view class="title">联系人信息</view>
      <view class="peoples">
        <view class="lines">
          <text class="room-num" @tap="doSelectPeople(contact, 'phone')">
            联系人
            <text v-if="contact.name != ''" class="name">{{ contact.name }}</text>
            <text v-else class="placeholder">请输入旅客信息</text>
          </text>
          <text v-if="contact.name != ''" class="iconfont icon-shanchu1" @tap="doEmptyPeople(contact)" />
          <text class="iconfont icon-address-book" @tap="doSelectPeople(contact, 'phone')"></text>
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="left">
        <text>{{ formatMoney(totalPrice) }}</text>
      </view>

      <view class="right">
        <MyButton :disable="!!formError" type="primary" @tap="doSubmit">立即支付</MyButton>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

.container {
  background: $page-bg-color;
  min-height: 100vh;
  position: relative;
  @include center(column);
  justify-content: flex-start;
  padding-bottom: 150rpx;
}

.infos,
.icard {
  margin-top: 20rpx;
  background: white;
  width: 100%;
}

.infos {
  @include center(column);
  align-items: flex-start;
  padding: 30rpx 40rpx 50rpx 28rpx;

  .normal {
    font-size: 28rpx;
    color: $black-color;
  }

  .title {
    font-weight: bold;
    font-size: 28rpx;
    color: $black-color;
    margin-bottom: 20rpx;
  }
}

.icard {
  width: 100%;

  .title {
    @include center();
    justify-content: flex-start;
    gap: 12rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: $black-color;
    padding: 30rpx 0 0 28rpx;

    & .tip {
      font-size: 20rpx;
      color: $black-color-v3;
    }
  }
}

.warn {
  @include center();
  justify-content: flex-start;
  font-size: 28rpx;
  color: #FF4D4F;
  margin-top: 20rpx;
}

.peoples {
  @include center(column);
  align-items: stretch;
  width: 100%;
  padding: 0 58rpx 50rpx;

  .lines {
    margin-top: 20rpx;
    padding: 12rpx 20rpx;
    width: 100%;
    @include center();
    justify-content: space-between;
    background: #EDEEF0;
    border-radius: 20rpx;


    .room-num {
      flex: 1;
      font-weight: bold;
      font-size: 28rpx;
      color: $black-color;

      .name,
      .placeholder {
        font-weight: normal;
      }

      .placeholder {
        color: $black-color-v3;
      }
    }

    .iconfont {
      font-size: 41rpx;
    }
  }
}

.footer {
  position: fixed;
  bottom: 0%;
  width: 100%;
  display: flex;
  background: white;
  height: 144rpx;
  @include center();
  justify-content: space-between;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  border-radius: 20rpx 20rpx 0rpx 0rpx;

  .left {
    margin-left: 28rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: #FF4D4F;
  }

  .right {
    margin-right: 28rpx;
  }
}
</style>