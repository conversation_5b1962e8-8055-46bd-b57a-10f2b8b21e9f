@import '@/styles/_define.scss';
@import '@/styles/_mix.scss';

.container {
  background: $page-bg-color;
  min-height: 100vh;
  padding-bottom: 170rpx;
}

.hotel-banner {
  height: 420rpx;

  swiper {
    width: 100%;
    height: 100%;

    swiper-item {
      width: 100%;
      height: 100%;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.hotel-cont {
  margin: 0rpx auto;
  background: #ffffff;
  border-radius: $border-radius-middle $border-radius-middle 0rpx 0rpx;
  box-shadow: 0rpx 3rpx 12rpx 0rpx rgba(0, 0, 0, 0.10);
  position: relative;
  top: -50rpx;
  padding: $padding-page;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: $padding-small;
  font-size: $fontsize-small;

  .title {
    font-size: $h3-v2;
    font-weight: 500;
  }

  .hotel-name {
    @include ellipse();
  }
}

.card {
  margin: 0 $padding-page $padding-page $padding-page;
  background: white;
  padding: $padding-v2;
  border-radius: $border-radius-v2;

  .title {
    font-weight: 500;
  }

  .content {
    padding: $padding-v2 0;
  }
}

.notice {
  .content {
    view {
      &:before {
        content: ' ';
        width: 13rpx;
        height: 13rpx;
        background: $plan-color-3;
      }

      @include center();
      justify-content: flex-start;
      margin-bottom: $padding-small;
      font-size: $fontsize-small;
      gap: $padding-small;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.info {
  .attrs, .confirm-cancel {
    display: flex;
    gap: $padding;
  }

  .attrs {
    font-weight: bold;
  }

  .confirm-cancel {
    margin: $padding 0;

    > view {
      border: 1rpx solid $border-color;
      padding: calc($padding / 2) $padding;
    }

    .cancel {
      color: $warning-color;
      border-color: $warning-color;
    }
  }
}

.room-number {
  @include center();
  justify-content: space-between;

  .remain-rooms {
    color: $warning-color;
  }
}

.invalid-data {
  display: flex;
  gap: $padding;

  > view {
    max-width: 30%;
    @include ellipse();
  }
}

.footer {
  position: fixed;
  bottom: 0%;
  width: 100%;
  @include center();
  justify-content: space-between;
  background: white;
  padding: $padding-page;

  .left {
    @include center();
    justify-content: flex-start;
    gap: $padding-small;

    .view-daily {
      @include center();
      gap: $padding-small;
    }

    .price {
      color: $price-color;
      font-weight: bold;
      font-size: $h3-v2;
    }
  }

  .apply {
    &.disable {
      background: $gray-color;
    }
  }
}