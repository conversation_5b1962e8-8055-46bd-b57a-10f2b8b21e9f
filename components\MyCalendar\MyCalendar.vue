<template>
  <view class="calendar">
    <view class="header">
      <text class="iconfont icon-fanhui" @tap="onMonth(-1)"></text>
      <text>{{ selectMonth.format('YYYY-MM') }}</text>
      <text class="iconfont icon-gengduo" @tap="onMonth(1)"></text>
    </view>
    <view class="weekly">
      <view>日</view>
      <view>一</view>
      <view>二</view>
      <view>三</view>
      <view>四</view>
      <view>五</view>
      <view>六</view>
    </view>
    <view class="days">
      <template v-if="type == 'range'">
        <view v-for="(item, index) in allDays" :key="index" :class="itemClass(item)" @tap="onTap(item)">
          <view class="holiday">{{ myHolidays[item.unix()] ?? (today.isSame(item, 'day') ? '今天' : '') }}</view>
          <view class="item-bottom">
            <view class="value">
              {{ item.date() }}
            </view>
            <view v-if="$slots.label" class="label">
              <slot :date="item.toDate()" name="label"></slot>
            </view>
          </view>
        </view>
      </template>
      <template v-else-if="type == 'date'">
        <view v-for="(item, index) in allDays" :key="index" :class="itemClass(item)" @tap="onTap(item)">
          <view class="holiday">{{ myHolidays[item.unix()] ?? (today.isSame(item, 'day') ? '今天' : '') }}</view>
          <view class="value">
            {{ item.date() }}
          </view>
          <view v-if="$slots.label" class="label">
            <slot :date="item.toDate()" name="label"></slot>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
import {holiday} from '@/api'
import dayjs from 'dayjs'
import {computed, onMounted, ref} from 'vue'

const props = defineProps({
  value: {
    type: [Array, Date, String],
    default() {
      return []
    }
  },
  month: {
    type: String,
    default() {
      return dayjs().format('YYYY-MM')
    }
  },
  type: {
    type: String,
    default: 'range'
  },
  start: {
    type: [String, Date],
    default: () => new Date()
  },
})
const emit = defineEmits(['monthChange', 'change'])

const today = dayjs()
const selectMonth = ref(dayjs(props.month + '-01', 'YYYY-MM-DD'))
const myValue = ref(props.value)
const myHolidays = ref({})

function itemClass(item) {
  const list = ['item'];
  if (item.day() == 6 || item.day() == 0) {
    list.push('sunday')
  }
  if ((item.month() !== selectMonth.value.month()) || (props.start && dayjs(props.start).isAfter(item, 'day'))) {
    list.push('other')
  }

  if (props.type === 'range') {
    if (isSameDate(item, 0)) {
      list.push('start')
    }
    if (isSameDate(item, 1)) {
      list.push('end')
    }
    if (isInRange(item)) {
      list.push('range')
    }
  } else {
    if (myValue.value && dayjs(myValue.value).isSame(item)) {
      list.push('active')
    }
  }

  if (today.isSame(item, 'day')) {
    list.push('today')
  }

  return list
}

const allDays = computed(() => {
  const list = []

  const start = selectMonth.value.startOf('month')
  let current = start.clone()

  const firstDayOfMonth = start.day()
  for (let i = 0; i < firstDayOfMonth; i++) {
    const c = start.add(-(firstDayOfMonth - i), 'day')
    list.push(c.clone())
  }

  while (current.month() == start.month()) {
    list.push(current.clone())

    current = current.add(1, 'day')
  }

  let last = list[list.length - 1]
  const lastDayOfMonth = last.day()

  for (let i = 6; i > lastDayOfMonth; i--) {
    last = last.add(1, 'day')

    list.push(last.clone())
  }

  return list
})

function onTap(item) {
  if (props.start && dayjs(props.start).isAfter(item, 'day')) {
    return
  }

  if (props.type == 'range') {
    if (!myValue.value[0]) {
      myValue.value[0] = item.toDate()
    } else if (!myValue.value[1]) {
      myValue.value[1] = item.toDate()
    } else {
      myValue.value = [item.toDate()]
    }

    if (myValue.value.length == 2) {
      if (dayjs(myValue.value[0]).isBefore(dayjs(myValue.value[1]))) {
        emit('change', [...myValue.value])
      } else {
        myValue.value = [myValue.value[1]]
      }
    }
  } else {
    myValue.value = item.toDate()
    emit('change', myValue.value)
  }
}

function isInRange(d) {
  if (myValue.value.length != 2) {
    return
  }

  const t = d.unix()

  return t >= dayjs(myValue.value[0]).unix() && t <= dayjs(myValue.value[1]).unix()
}

function onMonth(direction = 1) {
  let month = null
  if (direction > 0) {
    month = selectMonth.value.add(1, 'month')
  } else {
    month = selectMonth.value.add(-1, 'month')
  }

  selectMonth.value = month

  const keys = Object.keys(myHolidays.value)

  if (keys.length > 0) {
    const d = dayjs.unix(keys[0])
    console.log(d.year(), selectMonth.value.year())

    if (d.year() != selectMonth.value.year()) {
      getHoliday()
    }
  }

  emit('monthChange', month.format('YYYY-MM'))
}

function isSameDate(d, index) {
  if (!myValue.value[index]) {
    return false
  }

  return dayjs(d).unix() == dayjs(myValue.value[index]).unix()
}

function getHoliday() {
  const parmas = {
    start: selectMonth.value.startOf('month').unix(),
    end: selectMonth.value.endOf('month').unix()
  }
  holiday(parmas).then(({data}) => {
    myHolidays.value = Object.fromEntries(
        data.holidays
            .filter((item) => item.festival)
            .map((item) => {
              return [item.date, item.festival]
            })
    )
  })
}

onMounted(() => {
  if (props.start) {
    selectMonth.value = dayjs(props.start).startOf('month')
  }

  if (myValue.value) {
    if (myValue.value instanceof String) {
      myValue.value = dayjs(myValue.value).toDate()
    }

    if (props.type === 'range') {
      if (!(myValue.value instanceof Array)) {
        myValue.value = []
      } else if (myValue.value.length === 2) {
        myValue.value = [dayjs(myValue.value[0]).startOf('date').toDate(), dayjs(myValue.value[1]).startOf('date').toDate()]
      }
    } else if (props.type === 'date') {
      myValue.value = dayjs(myValue.value).toDate()
    }

    const d = (myValue.value instanceof Array) ? myValue.value[0] : myValue.value
    selectMonth.value = dayjs(d).startOf('month')
  }

  getHoliday()
})
</script>

<style lang="scss" scoped>
@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.header {
  margin-bottom: 32rpx;
  @include center();
  justify-content: space-between;
  color: black;
}

.weekly {
  border-bottom: 1rpx solid $border-color-v2;
  padding: $padding-v2 0;
}

.weekly,
.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  row-gap: calc($padding-big / 2);

  view {
    @include center();
    // height: 46rpx;
  }
}

.days {
  padding: $padding-v2 0;

  .sunday {
    color: $plan-color-2;
  }

  .other {
    color: $font-color-gray;
  }

  .today {
    color: orange;
  }

  .value {
    width: 44rpx;
    height: 44rpx;
    @include center();
  }

  .start,
  .end,
  .range {
    background: $primary-color-v2;
    color: white;
    position: relative;
  }

  .active .value {
    background: $primary-color-v2;
    color: white;
    border-radius: 50%;
  }

  .start {
    border-top-left-radius: $border-radius-middle;
    border-bottom-left-radius: $border-radius-middle;

    .item-bottom {
      &::before {
        content: '去';
        left: 8rpx;
      }
    }
  }

  .end {
    border-top-right-radius: $border-radius-middle;
    border-bottom-right-radius: $border-radius-middle;

    .item-bottom {
      &::after {
        content: '回';
        right: 8rpx;
      }
    }
  }

  .start .item-bottom::before,
  .end .item-bottom::after {
    position: absolute;
    bottom: 5rpx;
    font-size: 12rpx;
  }

  .item {
    @include center(column);

    .label {
      font-size: 17rpx;
      font-weight: 500;
    }
  }
}

.holiday {
  font-size: $fontsize-mini;
  color: $plan-color-3;
  height: 27rpx;
  width: 70rpx;
  text-align: center;
}

.start .holiday,
.end .holiday {
  color: white;
}
</style>
