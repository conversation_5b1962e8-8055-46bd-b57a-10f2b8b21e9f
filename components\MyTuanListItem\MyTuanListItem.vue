<template>
	<view class="tuan-item" @tap="handleTuan(item)">
		<image mode="widthFix" :src="item.pic"></image>
		<view class="zone">{{item.zone_name}}</view>
		<view class="name">{{item.name}}</view>
		<view class="price">{{formatMoney(item.price)}}起/人</view>
	</view>
</template>

<script setup>
	import {
		formatMoney
	} from '../../utils/index.js'

	const props = defineProps({
		item: {
			type: Object,
			default: () => {}
		}
	})

	function handleTuan(row) {
		uni.navigateTo({
			url: '/pages/tuan/detail?id=' + row.id,
		})
	}
</script>

<style lang="scss" scoped>
	@import '../../styles/_define.scss';
	@import '../../styles/_mix.scss';

	.tuan-item {
		width: 50%;
		border-bottom: 1px solid $border-color;
		padding: $padding;
		display: flex;
		flex-direction: column;
		gap: 20rpx;

		&:nth-child(odd) {
			border-right: 1px solid $border-color;
		}

		.name {
			font-size: 30rpx;
			@include ellipse(2);
			max-height: 80rpx;
		}

		.price {
			color: $price-color;
			letter-spacing: 1rpx;
		}

		image {
			width: 100%;
			// max-height: 400rpx;
		}
	}
</style>