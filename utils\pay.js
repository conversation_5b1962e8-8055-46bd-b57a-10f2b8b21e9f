import {
	showToast
} from "."
import {
	orderPayConfirm
} from "../api/order"

export function doPay(data) {
	return new Promise((resolve, reject) => {
		const pay_id = data.pay_id
		const payStr = JSON.parse(data.pay_str)

		WeixinJSBridge.invoke('getBrandWCPayRequest', payStr, res => {
			if (res.err_msg != "get_brand_wcpay_request:ok") {
				showToast('支付失败', 'error').then(() => {
					reject(new Error(res.err_msg))
				})
				return
			}

			orderPayConfirm({
				pay_id
			}).then(res => {
				showToast('支付成功', 'success').then(() => {
					resolve(new Error(res.msg))
				}).catch(() => {
					reject()
				})
			})
		})
	})

}