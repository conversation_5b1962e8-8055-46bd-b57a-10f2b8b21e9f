@use "../../styles/_define.scss" as define;
@use "../../styles/_mix.scss" as mix;

// 遮罩层
.ios-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  @include mix.center();
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

// 主弹窗容器
.ios-payment-alert {
  width: 600rpx;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;

  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background: linear-gradient(135deg, #35C3FF 0%, #1890FF 100%);
    opacity: 0.1;
  }
}

// 内容区域
.content {
  @include mix.center(column);
  gap: 20rpx;
  margin-bottom: 50rpx;
  padding-top: 20rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: define.$black-color;
    text-align: center;
  }

  .subtitle {
    font-size: 28rpx;
    color: define.$black-color-v2;
    text-align: center;
    line-height: 1.5;
    padding: 0 20rpx;
  }
}

// 底部按钮
.footer {
  @include mix.center();

  .btn-confirm {
    width: 100%;
    height: 88rpx;
    @include mix.center();
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    background: linear-gradient(135deg, #35C3FF 0%, #1890FF 100%);
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(53, 195, 255, 0.3);

    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(53, 195, 255, 0.4);
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
