<script setup>
import { navTo } from '@/utils';

const keyword = defineModel()
const emits = defineEmits(['confirm'])
const props = defineProps({
  city: { default: () => ({}) },
  placeholder: String,
})

</script>


<template>
  <view class="search-bar">
    <view class="inner">
      <view class="left" @tap="navTo('pages/option/cities/cities')">
        <text class="iconfont icon-daohangdizhiweizhi f1"></text>
        <text>{{ city.city || '城市' }}</text>
        <text class="iconfont icon-xialajiantou f2"></text>
      </view>
      <view class="right">
        <text class="iconfont icon-sousuo1" />
        <input :placeholder="placeholder" :value="keyword" @confirm="emits('confirm', e)"
          @input="(e) => keyword = e.detail.value" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";


.search-bar {
  background: white;
  padding: 20rpx 28rpx;

  .inner {
    //height: 72rpx;
    @include center();
    gap: 4rpx;
    align-items: stretch;
    border: 2rpx solid #53B5B4;
    border-radius: 46rpx;
    padding: 22rpx 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 250, 228, 0.68) 100%),
      linear-gradient(180deg, #5BD8FF 0%, #9DFFF2 100%);
    background-blend-mode: hard-light;  
  }


  .left {
    border-radius: 36rpx 0 0 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 8rpx;
    width: 196rpx;
    //padding: 20rpx;

    .f1 {
      color: #1890FF;
    }

    .f2 {
      font-size: 20rpx;
    }
  }

  .right {
    border-left: 4rpx solid #53B5B4;
    flex: 1;
    @include center();
    justify-content: flex-start;
    padding-left: 26rpx;
    gap: 4rpx;

    .iconfont {
      color: #8BCBD7;
    }
  }
}
</style>
