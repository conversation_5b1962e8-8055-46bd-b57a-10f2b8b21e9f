<script setup>
import MyButton from '@/components/MyButton/MyButton.vue';
import MyInputNumber from '@/components/MyInputNumber/MyInputNumber.vue';
import { computed, onMounted, onUnmounted, ref, unref, watch } from "vue";
import { planDetail } from "@/api/plan";
import { hotelSearch } from "@/api/hotel";
import { sceneSearch } from "@/api/scene";
import MyCalendar from "@/components/MyCalendar/MyCalendar.vue";
import dayjs from "dayjs";
import { deepToRaw, formatMoney, hideLoading, navTo, showLoading, showToast } from "@/utils";
import { useGlobalStore } from "@/store/global";
import { GlobalTypePeople } from "@/utils/constmap";
import { OHotel, OPeople, OPolicy, OScene, OTicket } from './components';
import { productCalendar } from "@/api/product";
import UniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
import { orderSubmit } from "@/api/order";
import MyInfoPopup2 from "@/components/MyInfoPopup2/MyInfoPopup2.vue";
import MyChat from "@/components/MyChat/MyChat.vue";

const props = defineProps({
  query: {
    type: Object,
    default: () => ({})
  }
})

const globalStore = useGlobalStore()
const plan = ref({
  sections: []
})
const myChatData = ref({})
const start = ref('')
const cur = ref(0) //天的下标
const curTimelines = ref({})
const list = computed(() => curTimelines.value[cur.value] ?? []) // OHotel、OScene混合数组
const canNext = computed(() => {
  /** @type {Array<OPeople>} */
  const peoples = list.value.reduce((p, it) => {
    if (it instanceof OHotel) {
      p = p.concat(it.getPeoples())
    }
    if (it instanceof OScene) {
      p = p.concat(it.getPeoples())
    }
    return p
  }, [])
  if (!start.value) {
    return false
  }

  return !peoples.find(it => !it.name)
})
const curDate = computed(() => {
  let s = start.value
  let c = cur.value
  if (!s) {
    return null
  }
  const t = dayjs(s).add(c, 'day')
  return t.toDate()
})
const scrollToView = computed(() => {
  return `day-${cur.value}`
})
const maxItems = 5
const showSelectStartDate = ref(false)
const tabs = computed(() => {
  return plan.value.sections.map((it, index) => {
    return {
      value: index,
      day: start.value ? dayjs(start.value).add(index, 'day') : '',
    }
  })
})
const curEditPeople = ref(null)
const contacts = ref(new OPeople())
const selectedItems = computed(() => {
  return Object.values(curTimelines.value).flat().filter(it => {
    if (it instanceof OHotel) {
      return it.rooms.filter(room => {
        return room.policies.filter(policy => policy.quantity > 0)
      }).length > 0
    } else {
      return it.tickets.filter(it => it.quantity > 0).length > 0
    }
  })
})

const totalAmount = computed(() => {
  let total = 0
  for (const it of selectedItems.value) {
    if (it instanceof OHotel) {
      total += it.rooms.reduce((total, room) => {
        return total + room.policies.reduce((total, policy) => {
          return total + policy.quantity * policy.sales_price
        }, 0)
      }, 0)
    } else {
      total += it.tickets.reduce((total, ticket) => {
        return total + ticket.quantity * ticket.sale_price
      }, 0)
    }
  }

  return total
})
const showChat = ref(false)

watch(cur, () => {
  fetchList().then(() => getQuotes())
})

watch(() => globalStore.data, (data) => {
  switch (data.type) {
    case GlobalTypePeople:
      if (data.data.length > 0) {
        Object.assign(curEditPeople.value, data.data[0])
      }
      break
  }
})

function doSelectPeople(people, required = '') {
  curEditPeople.value = people
  navTo('pages/peoples/peoples', { choose: 1, required })
}

//刷新列表对象里的date值,在日期变更、新对象插入替换时需要刷新对象里保存的日期
function refreshListDates() {
  let date = unref(curDate.value)
  list.value.forEach(it => {
    if (it instanceof OHotel) {
      it.date = [date, dayjs(date).add(1, 'day').toDate()]
    }
    if (it instanceof OScene) {
      it.date = date
    }
  })
}

function onSelectStartDate(d) {
  showSelectStartDate.value = false
  start.value = d
  refreshListDates()
  getQuotes(true)
}

//  获取门票和酒店报价
async function getQuotes(force = false) {
  showLoading('获取全网报价...')

  for (const it of list.value) {
    try {
      if (it instanceof OHotel) {
        if (it?.rooms?.length > 0 && !force) {
          continue
        }

        await it.loadRooms()

        it.cursor = it.rooms.length > maxItems ? maxItems : it.rooms.length
      } else if (it instanceof OScene) {
        if (it?.tickets?.length > 0 && !force) {
          continue
        }
        await it.loadTickets()
        for (const ticket of it.tickets) {
          const params = {
            product_id: ticket.ticket_id,
            product_type: ticket.product_type,
            start: dayjs(it.date).format('YYYY-MM-DD'),
          }
          const { list } = await getTicketPrice(params)
          const price = list.filter(it => it.date === params.start)
          ticket.quantity = 0

          if (price.length > 0) {
            Object.assign(ticket, price[0], { has_price: true })
          }
        }
        it.tickets = it.tickets.filter(it => it.has_price)

        // 只显示最多指定个数
        it.cursor = it.tickets.length > maxItems ? maxItems : it.tickets.length
      }
    } catch (e) {

    }
  }
  hideLoading()
}

async function getTicketPrice(params) {
  const { data } = await productCalendar(params, false)
  return data
}

function makePeoples2Quantity(peoples, quantity) {
  const length = peoples.length || 0

  if (length > quantity) {
    peoples.splice(quantity, length - quantity)
    return peoples
  }
  return peoples.concat(Array(quantity - length).fill(0).map(() => new OPeople))
}

/**
 * @param {OTicket} ticket
 */
function onChangeQuantityTicket(ticket, quantity) {
  ticket.quantity = quantity
  ticket.peoples = makePeoples2Quantity(ticket.peoples, quantity)
}

/**
 * @param {OPolicy} policy
 */
function onChangeQuantityRoomPolicy(policy, quantity) {
  policy.quantity = quantity
  policy.room.quantity = policy.room.policies.reduce((total, it) => total + it.quantity, 0)
  policy.peoples = makePeoples2Quantity(policy.peoples, quantity)
}

function onNext() {
  if (!start.value) {
    showToast('请选择出发日期')
    return;
  } else if (!canNext.value) {
    showToast('请填写出行人和联系人信息')
    return
  }

  if (cur.value < plan.value.sections.length - 1) {
    cur.value++
    return
  }

  if (!contacts.value.name) {
    showToast('请选择联系人')
    return
  }
  submitOrder()
}

function submitOrder() {
  const products = []

  selectedItems.value.forEach(it => {
    if (it instanceof OHotel) {
      it.rooms.forEach(room => {
        room.policies.forEach(policy => {
          const quantity = Number(policy.quantity)
          if (!quantity || quantity < 0) {
            return
          }
          const item = {
            product_type: room.ota.product_type,
            product_id: Number(it.id),
            num: quantity,
            sale_price: policy.sales_price,
            peoples: policy.peoples.map(p => {
              return {
                name: p.name,
              }
            }),
            extra: {
              start: dayjs(it.date[0]).unix(),
              end: dayjs(it.date[1]).unix(),
              ota_id: room.ota.ota_id,
              ota_room_id: room.id,
              ota_policy_id: policy.id,
            }
          }

          products.push(item)
        })
      })
    } else {
      it.tickets.forEach(ticket => {
        const quantity = Number(ticket.quantity)
        if (!quantity || quantity < 0) {
          return
        }
        const item = {
          product_type: ticket.product_type,
          product_id: Number(it.id),
          num: quantity,
          sale_price: ticket.sale_price,
          peoples: deepToRaw(ticket.peoples),
          extra: {
            date: dayjs(it.date).unix(),
            ota_id: ticket.ticket_id,
          }
        }
        products.push(item)
      })
    }
  })

  if (products.length === 0) {
    showToast('请选择相关产品')
    return
  }

  const data = {
    order_type: 'travel',
    total_price: totalAmount.value,
    name: contacts.value.name,
    tel: contacts.value.phone,
    products: JSON.stringify(products),
    plan_id: plan.value.plan_id
  }

  orderSubmit(data).then(({ data }) => {
    const { order_id } = data
    showToast('下单成功').then(() => {
      navTo('pages/pay/pay', { order_id }, true)
    })
  })
}

async function fetchList() {
  if (curTimelines.value[cur.value]) {
    return
  }

  const hotelIds = []
  const sceneIds = []

  let timeline = plan.value.sections[cur.value].timeline

  timeline.forEach(item => {
    if (['hotel', 'scene'].includes(item.type) && item.item_id > 0) {
      (item.type === 'hotel' ? hotelIds : sceneIds).push(item.item_id)
    }
  })

  let ret = []
  let sceneList = []
  let hotelList = []

  if (hotelIds.length) {
    ret.push(hotelSearch({ ids: hotelIds.join(',') }).then(({ data }) => (hotelList = data.list)))
  }
  if (sceneIds.length) {
    ret.push(sceneSearch({ ids: sceneIds.join(',') }).then(({ data }) => (sceneList = data.list)))
  }
  await Promise.all(ret)

  timeline = [...sceneList.map(it => {
    let o = new OScene(it)
    o.date = deepToRaw(curDate.value)

    return o
  }), ...hotelList.map(it => {
    let o = new OHotel(it)
    let date = deepToRaw(curDate.value)
    o.date = !date ? null : [date, dayjs(date).add(1, 'day').toDate()]

    return o
  })]

  const finder = (item) => {
    if (item instanceof OHotel) {
      return v => (v.type == 'hotel' && v.item_id === item.id)
    } else {
      return v => (v.type == 'scene' && v.item_id === item.id)
    }
  }
  timeline.sort((a, b) => {
    const indexA = plan.value.sections[cur.value].timeline.findIndex(finder(a))
    const indexB = plan.value.sections[cur.value].timeline.findIndex(finder(b))

    return indexA - indexB
  })
  curTimelines.value[cur.value] = timeline
}

/**
 *
 * @param {OPeople} people
 */
function onRemovePeople(people) {
  people.reset()
}

function onShowSelectStartDate() {
  showSelectStartDate.value = true
}

function onTapGoIndex(index) {
  if (!start.value) {
    showToast('请先选择出发日期')
    return
  }
  cur.value = index
}

//执行酒店替换
async function onSelectHotel({ hotel }) {
  const { data } = await hotelSearch({ ids: hotel.id })
  // debugger
  let idx = curTimelines.value[cur.value].findIndex(v => v instanceof OHotel && v.id === myChatData.value.hotel_id)
  if (idx > -1) { //查找到，则替换并更新房型
    curTimelines.value[cur.value].splice(idx, 1, new OHotel(data.list[0]))
    let it = curTimelines.value[cur.value][idx]
    showChat.value = false
    refreshListDates()
    await it.loadRooms()
    it.cursor = it.rooms.length > maxItems ? maxItems : it.rooms.length
  }
}

function onReplaceHotel(index) {
  const hotel = list.value[index]
  const scene = list.value[index - 1]

  myChatData.value.hotel_id = hotel.id
  if (scene) {
    myChatData.value.scene_id = scene.id
  }

  showChat.value = true
}

onUnmounted(() => {
  globalStore.clearData()
})

onMounted(query => {
  planDetail({ plan_id: props.query.plan_id }).then(({ data }) => {
    Object.assign(plan.value, data)

    if (plan.value.prompt_options.start_date) {
      start.value = dayjs.unix(plan.value.prompt_options.start_date).toDate()
    } else {
      showSelectStartDate.value = true
    }

    fetchList().then(() => {

    })
  })
})

</script>

<template>
  <view class="container">
    <view class="days">
      <scroll-view :scroll-into-view="scrollToView" :show-scrollbar="false" enhanced scroll-x>
        <view v-for="(item, index) in tabs" :id="`day-${index}`" :key="index" class="day">
          <view :class="{
            active: cur === index,
            'select-date': start
          }" @tap="onTapGoIndex(index)">
            <view>
              第
              <text class="value">{{ index + 1 }}</text>
              天
            </view>
            <view>{{ item.day ? item.day.format('M月D日') : '' }}</view>
          </view>
        </view>

      </scroll-view>
      <text class="iconfont icon-rili calendar" @tap="onShowSelectStartDate"></text>
    </view>

    <scroll-view :scroll-x="false" :scroll-y="true" enhanced :show-scrollbar="false">
      <view v-for="(item, index) in list" :key="index" class="form-card">
        <view class="product">
          <view class="left">
            <view class="form-card-index">
              <text>{{ index + 1 }}</text>
            </view>
            <image :src="item.pic" class="img" mode="aspectFill"></image>
          </view>

          <view class="right">
            <text class="line title">{{ item.name }}</text>
            <text v-if="item.tel" class="line">{{ item.tel }}</text>
            <text v-if="item.type === 'scene'" class="line">{{ item.open_time || '全天' }}</text>
            <text class="line">{{ item.address }}</text>
            <view v-if="item instanceof OHotel" class="hotel-switch">
              <view @tap="onReplaceHotel(index)">
                <text class="iconfont icon-qiehuan1"></text>
                更换
              </view>
            </view>
          </view>
          <view :class="item instanceof OScene ? 'scene' : ''" class="item-icon">
            <text :class="{
              'icon-chuang': (item instanceof OHotel),
              'icon-jingdian': (item instanceof OScene),
            }" class="iconfont"></text>
          </view>
        </view>

        <view v-if="(item instanceof OHotel)" class="icard">
          <view class="title">选择房型</view>
          <view class="inner">
            <template v-if="item.rooms?.length">
              <transition-group name="list" tag="view">
                <view v-for="room in item.rooms.slice(0, item.cursor)" :key="room.id" class="policy">
                  <view class="room">
                    <view class="rinfo">
                      <view class="left">
                        <image :src="room.pic" mode="aspectFill" />
                        <view>
                          <text class="name">{{ room.name }}</text>
                          <text v-if="room.attrs?.length" class="lines">{{ room.attrs.join(' ') }}</text>
                          <text class="lines">{{ room.bed_desc }}</text>
                        </view>

                      </view>
                      <view class="right">
                        <text :class="{
                          up: room.open
                        }" class="iconfont icon-xiangxia" @tap="room.open = !room.open"></text>
                        <view>
                          ￥
                          <text class="price">{{ formatMoney(room.sale_price, 2, '') }}</text>
                          均
                        </view>

                      </view>
                    </view>
                    <template v-if="room.open">
                      <view v-for="policy in room.policies" :key="policy.id" :class="{
                        active: policy.quantity > 0
                      }" class="rinfo rextra">
                        <view class="left">
                          <view class="name">
                            <view>{{ policy.name }}</view>

                            <text v-if="policy.cancel_rule" class="tip">{{ policy.cancel_rule }}</text>
                          </view>
                          <view>
                            {{ policy.confirm_desc }} 可住{{ policy.person }}人 锁定房源
                          </view>

                        </view>
                        <view class="right">
                          <text class="price">{{ formatMoney(policy.sales_price) }}</text>
                          <MyInputNumber v-model="policy.quantity" :max="policy.max_amount ?? 10" :min="0"
                            class="input-number" @change="v => {
                              onChangeQuantityRoomPolicy(policy, v)
                            }" />
                        </view>
                      </view>
                    </template>

                  </view>
                </view>
              </transition-group>

              <uni-load-more v-if="maxItems < item.rooms.length"
                :content-text="{ contentdown: item.cursor <= maxItems ? '点击显示更多' : '点击收起' }" status="more"
                @click-load-more="() => {
                  item.cursor = item.cursor <= maxItems ? item.rooms.length : maxItems
                }" />

            </template>
            <view v-else class="empty-tickets">
              <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/order/ticket-empty.png" />
              {{ !start ? '请选择出发时间' : '暂无可预订房间' }}
            </view>
          </view>
        </view>

        <view v-if="(item instanceof OScene)" class="icard">
          <view class="title">门票类型</view>
          <view class="inner">
            <template v-if="item.tickets?.length">
              <view class="tickets">
                <view v-for="ticket in item.tickets.slice(0, item.cursor)" :key="ticket.ticket_id" :class="{
                  active: ticket.quantity > 0
                }" class="lines">
                  <view>
                    <view class="ticket">
                      <view class="left">
                        <text class="ticket-title">{{ ticket.title }}</text>
                        <text class="tip">{{ ticket.ticket_type }}</text>
                      </view>
                      <text class="price">
                        {{ formatMoney(ticket.sale_price) }}
                      </text>
                    </view>
                    <MyInputNumber v-model="ticket.quantity" :min="0" class="input-number" @change="(v) => {
                      onChangeQuantityTicket(ticket, v)
                    }" />
                  </view>

                </view>
              </view>
              <uni-load-more v-if="maxItems < item.tickets.length"
                :content-text="{ contentdown: item.cursor <= maxItems ? '点击显示更多' : '点击收起' }" status="more"
                @click-load-more="() => {
                  item.cursor = item.cursor <= maxItems ? item.tickets.length : maxItems
                }" />

            </template>
            <view v-else-if="item.isFree()">
              <view class="tickets">
                <view class="lines active">
                  <view>
                    <view class="ticket">
                      <view class="left">
                        <text class="ticket-title">门票</text>
                        <text class="tip">{{ `${item.name}免费开放（无需购票）` }}</text>
                      </view>
                    </view>
                    <view style="color: #1890FF">
                      免费
                    </view>
                  </view>

                </view>
              </view>
            </view>
            <view v-else class="empty-tickets">
              <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/order/ticket-empty.png" />
              {{ !start ? '请选择出发时间' : '暂无可预订门票' }}
            </view>
          </view>
        </view>

        <view v-if="item.getPeoples().length > 0" class="icard peoples">
          <view class="title">使用人</view>
          <view class="inner">
            <view class="peoples">
              <template v-if="(item instanceof OScene) || (item instanceof OHotel)">
                <view v-for="(pitem, pi) in item.getPeoples()" :key="pi" :class="{
                  empty: !pitem.name
                }" class="people-item">
                  <text class="number" @tap="doSelectPeople(pitem)">{{ `出行人${pi + 1}` }}</text>
                  <text class="name" @tap="doSelectPeople(pitem)">
                    {{ pitem.name ?? '请输入出行人信息' }}
                  </text>
                  <text v-if="pitem.name" class="iconfont icon-shanchu1" @tap="onRemovePeople(pitem)" />
                  <text class="iconfont icon-address-book" @tap="doSelectPeople(pitem)"></text>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>
      <view v-if="cur === plan.sections.length - 1" class="contacts">
        <view class="title">联系人</view>
        <view class="inner">
          <view :class="{ empty: !contacts.name }" class="people-item">
            <text class="number" @tap="doSelectPeople(contacts, 'phone')">联系人</text>
            <text class="name" @tap="doSelectPeople(contacts, 'phone')">{{
              contacts.name ?? '请输入联系人信息'
            }}
            </text>
            <text class="iconfont icon-address-book" @tap="doSelectPeople(contacts, 'phone')"></text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="next-btn">
      <view>
        合计:
        <text class="total-amount">{{ formatMoney(totalAmount) }}</text>
      </view>
      <my-button :disable="!canNext" type="primary" @tap="onNext">
        {{ cur !== plan.sections.length - 1 ? `完成第${cur + 1}天` : '完成' }}
      </my-button>
    </view>

    <my-info-popup2 v-if="showSelectStartDate" :action-text="'确定'" :close="false" :mask-click="false" title="请设置出发时间"
      type="bottom" @close="showSelectStartDate = false">
      <MyCalendar :start="dayjs().add(1, 'day').toDate()" :value="start" type="date" @change="onSelectStartDate" />
    </my-info-popup2>

    <MyInfoPopup2 v-if="showChat" custom-class="min-chat" @close="showChat = false">
      <view style="height: 70vh">
        <MyChat :can-open-menu="false" :model-value="myChatData" :show-hello="false" :show-hotel-replace-btn="true"
          :show-view-more-hotel="false" chat-type="hotel" @hotel-click="onSelectHotel" />
      </view>

    </MyInfoPopup2>

  </view>
</template>

<style lang="scss" scoped>
@import 'myorderpreviewpackage';
</style>
