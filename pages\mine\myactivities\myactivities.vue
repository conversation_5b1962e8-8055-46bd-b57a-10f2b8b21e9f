<template>
  <MyRefreshList :data="list" :refreshing="refreshing" :loading="loading" :hasMore="hasMore" height="100vh"
                 @refresh="onRefresh" @load-more="onLoadMore" @refresh-timeout="handleRefreshTimeout"
                 @load-more-timeout="handleLoadMoreTimeout">
    <template #default="{ item }">
      <view class="activity-item">
        <!-- 上半部分：图片区域 -->
        <view class="image-section">
          <image :src="(item as any).image" class="activity-image" mode="aspectFill"/>
          <!-- 活动状态标签 -->
          <view class="status-tag" :class="{ 'ended': !(item as any).isActive }">
            <image class="status-icon" :src="(item as any).statusIcon" mode="aspectFill"></image>
            <text class="status-text">{{ (item as any).status }}</text>
          </view>
        </view>

        <!-- 下半部分：信息区域 -->
        <view class="info-section">
          <!-- 活动标题 -->
          <text class="activity-title">{{ (item as any).title }}</text>

          <!-- 奖励信息区域 -->
          <view class="reward-info">
            <image class="reward-icon" src="/static/gift.png" mode="aspectFill"></image>
            <text class="reward-text">{{ (item as any).reward }}</text>
          </view>

          <!-- 底部信息区域 -->
          <view class="bottom-info">
            <view class="time-info">
              <image class="time-icon" src="/static/time_icon.png" mode="aspectFill"></image>
              <text class="end-time">结束：{{ (item as any).endTime }}</text>
            </view>
            <view class="action-button" @click="handleActionClick(item)">
              <text class="button-text">查看</text>
            </view>
          </view>
        </view>
      </view>
    </template>
  </MyRefreshList>
</template>
<script setup lang="ts">
import MyRefreshList from '@/components/MyRefreshList/MyRefreshList.vue';
import {ref} from 'vue';
import {showToast, navTo} from '../../../utils'
import {onShow} from '@dcloudio/uni-app';
import {getMyActivities} from '@/api/myactivities';

const list = ref<any[]>([])
const refreshing = ref(false)
const loading = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = ref(10)

// 时间格式化函数
function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp * 1000)
  const year = date.getFullYear()
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 处理按钮点击事件
const handleActionClick = (item: any) => {
  navTo(item.link)
}

// 判断活动是否进行中
function isActivityActive(state: number, endTimestamp: number): boolean {
  const currentTimestamp = Math.floor(Date.now() / 1000)
  return state === 1 && currentTimestamp < endTimestamp
}

// 状态映射函数
function getStatusText(state: number, endTimestamp: number): string {
  return isActivityActive(state, endTimestamp) ? '进行中' : '已结束'
}

// 获取状态图标
function getStatusIcon(state: number, endTimestamp: number): string {
  return isActivityActive(state, endTimestamp) ? '/static/in_progress.png' : '/static/stopped.png'
}

// 获取奖励类型名称
function getRewardTypeName(rewardType: number): string {
  const typeMap: Record<number, string> = {
    1: '积分',
    2: '机上wifi',
    3: '实物奖励',
    4: '积分'
  }
  return typeMap[rewardType] || '其他奖励'
}

// 奖励格式化函数
function formatRewards(rewards: Array<{ reward_type: number, amount: number }>): string {
  if (!rewards || rewards.length === 0) {
    return '暂无奖励'
  }

  const rewardTexts = rewards.map(reward => {
    const typeName = getRewardTypeName(reward.reward_type)
    return `${typeName}+${reward.amount}`
  })

  return `奖品:${rewardTexts.join(',')}`
}

// 数据转换函数
function transformActivityData(apiData: any): any {
  const isActive = isActivityActive(apiData.state, apiData.end)
  return {
    id: apiData.activity_id,
    title: apiData.title,
    image: apiData.cover,
    status: getStatusText(apiData.state, apiData.end),
    statusIcon: getStatusIcon(apiData.state, apiData.end),
    isActive: isActive,
    reward: formatRewards(apiData.rewards),
    buttonText: isActive ? '立即参与' : '已结束',
    endTime: formatTimestamp(apiData.end),
    link: apiData.link
  }
}

async function onRefresh() {
  refreshing.value = true
  page.value = 1
  hasMore.value = true
  try {
    const response = await getMyActivities({
      page: page.value,
      page_size: pageSize.value
    })

    if (response.code === 0 && response.data) {
      const transformedList = response.data.list.map(transformActivityData)
      list.value = transformedList

      // 判断是否还有更多数据
      hasMore.value = response.data.list.length >= pageSize.value
    } else {
      showToast(response.msg || '获取活动列表失败')
      list.value = []
    }
  } catch (error) {
    console.error('获取活动列表失败:', error)
    showToast('网络错误，请重试')
    list.value = []
  } finally {
    refreshing.value = false
  }

}

async function onLoadMore() {
  if (!hasMore.value) return

  loading.value = true
  page.value += 1

  try {
    const response = await getMyActivities({
      page: page.value,
      page_size: pageSize.value
    })

    if (response.code === 0 && response.data) {
      const transformedList = response.data.list.map(transformActivityData)
      list.value.push(...transformedList)

      // 判断是否还有更多数据
      hasMore.value = response.data.list.length >= pageSize.value
    } else {
      showToast(response.msg || '加载更多失败')
      page.value -= 1 // 回退页码
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    showToast('网络错误，请重试')
    page.value -= 1 // 回退页码
  } finally {
    loading.value = false
  }
}

const handleRefreshTimeout = () => {
  console.warn('刷新超时')
  refreshing.value = false // 重置刷新状态
  showToast('刷新超时，请重试')
}

// 加载更多超时处理 - 组件内部已自动重置loading状态
const handleLoadMoreTimeout = () => {
  console.warn('加载更多超时')
  // 注意：不需要手动重置loading状态，组件内部已处理
  showToast('加载超时，请重试')
}

onShow(() => {
  onRefresh()
})

</script>


<style lang="scss" scoped>
@import './myactivities.scss';
</style>