<template>
  <view :id="id" ref="child" class="yj-ui-tooltip">
    <view class="slot" @tap="e => emits('tap', e)">
      <slot></slot>
    </view>
    <view v-if="show" :style="contentStyles" class="yj-ui-tooltip-content">
      <view class="yj-ui-tooltip-content-inner">
        <view v-if="$slots.icon" class="yj-ui-tooltip-icon">
          <slot name="icon"></slot>
        </view>
        <view class="yj-ui-tooltip-text">
          <slot name="content">{{ content }}</slot>
        </view>
      </view>
    </view>
    <view :style="arrowStyles" class="yj-ui-tooltip-arrow"></view>
  </view>
</template>

<script setup>
import {useGlobalStore} from '@/store/global';
import {computed, getCurrentInstance, nextTick, onMounted, ref, watch} from 'vue';

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  placement: {
    type: String, //top、left、bottom、right
    default: 'top'
  },
  conflict: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: '',
    required: true,
  },
})

const emits = defineEmits(['tap'])
const globalStore = useGlobalStore()

const position = ref({
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  height: 0,
  width: 0
})
const id = computed(() => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
})

const contentStyles = ref({})
const arrowStyles = ref({})
const show = computed(() => {
  if (globalStore.viewedTips.includes(props.type)) {
    return false
  } else if (props.conflict.length > 0) {
    const index = props.conflict.findIndex(item => item === props.type)
    if (index > 0) {
      const prev = props.conflict[index - 1]
      if (prev && globalStore.viewedTips.includes(prev)) {
        return true
      } else {
        return false
      }
    }
    return true
  }

  return true
})

const instance = getCurrentInstance()

watch(show, () => {
  if (show.value) {
    render()
  } else {
    contentStyles.value.visibility = 'hidden'
    arrowStyles.value.visibility = 'hidden'
  }
})

function render() {
  globalStore.registerEvent('pageTap', () => {
    globalStore.viewTip(props.type)
  })

  nextTick(async () => {
    const po = await select(`#${id.value}`)
    if (!po) {
      return
    }
    Object.assign(position.value, po)

    nextTick(() => {
      uni.getSystemInfo({
        success: async ({screenWidth}) => {

          //width、height、top、left、right、bottom
          const [rect, arrow, wrap] = await Promise.all([
            select(`#${id.value} .yj-ui-tooltip-content`),
            select(`#${id.value} .yj-ui-tooltip-arrow`),
            select(`#${id.value} .slot`),
          ])
          if (!rect || !arrow) {
            return
          }

          let arrowLeft, arrowTop
          if (props.placement == 'top') {
            //控制箭头方向
            // Object.assign(arrowStyles.value, {
            //   transform: 'rotate(0deg)',
            // })
            arrowTop = 0 - arrow.height
            arrowLeft = (wrap.width - arrow.width) / 2

          } else if (props.placement == 'bottom') {
            //控制箭头方向
            Object.assign(arrowStyles.value, {
              transform: 'rotate(180deg)',
            })
            arrowLeft = (wrap.width - arrow.width) / 2
            arrowTop = wrap.height
          } else if (props.placement == 'left') {
            //控制箭头方向
            Object.assign(arrowStyles.value, {
              transform: 'rotate(270deg)',
              transformOrigin: 'top left',
            })
            arrowLeft = 0 - arrow.height
            arrowTop = (arrow.width + wrap.height) / 2
          } else if (props.placement == 'right') {
            //控制箭头方向
            Object.assign(arrowStyles.value, {
              transform: 'rotate(90deg)',
              transformOrigin: 'top left',
            })
            arrowLeft = wrap.width + arrow.height
            arrowTop = (wrap.height - arrow.width) / 2
          }

          Object.assign(arrowStyles.value, {
            left: arrowLeft + 'px',
            top: arrowTop + 'px',
            visibility: 'visible'
          })

          let contentLeft, contentTop
          if (['top', 'bottom'].includes(props.placement)) {
            let indent = uni.rpx2px(20) //浮层边框向外再额外探出一部分
            contentLeft = 0 - indent
            // 检查右边空间是否足够
            if (rect.left + rect.width > screenWidth) {
              contentLeft = 0 - rect.width + wrap.width + indent
            }
            if (props.placement == 'top') {
              // 最后额外往内容区靠近几像素使浮层与三角贴合
              contentTop = (0 - rect.height - arrow.height) + uni.rpx2px(2)
            } else {
              contentTop = (wrap.height + arrow.height) - uni.rpx2px(2)
            }
          } else {
            contentTop = (wrap.height - rect.height) / 2
            if (props.placement == 'left') {
              contentLeft = 0 - arrow.height - rect.width + uni.rpx2px(2)
            } else {
              contentLeft = wrap.width + arrow.height - uni.rpx2px(2)
            }
          }

          contentStyles.value = Object.assign({
            top: contentTop + 'px',
            left: contentLeft + 'px',
            visibility: 'visible'
          })
        }
      })
    })
  })
}

onMounted(() => {
  if (!show.value) {
    return
  }

  render()
})

async function select(str) {
  return new Promise((resolve, reject) => {
    uni.createSelectorQuery().in(instance.proxy).select(str).boundingClientRect(res => {
      resolve(res)
    }).exec()
  })

}

</script>

<style lang="scss" scoped>
.yj-ui-tooltip {
  position: relative;
  overflow: visible;
}

.yj-ui-tooltip-box {
  position: absolute;
  display: flex;
  flex-direction: column;
}

.yj-ui-tooltip-content,
.yj-ui-tooltip-arrow {
  visibility: hidden;
}

.yj-ui-tooltip-content {
  background: #FACA14;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  width: auto;
  top: 0;
  left: 0;
  position: absolute;
  overflow: visible;
}

.yj-ui-tooltip-content-inner {
  display: flex;
  align-items: flex-end;
  white-space: nowrap;
  overflow: visible;
}

.yj-ui-tooltip-icon {
  margin-right: -4rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-top: -40rpx;
}

.yj-ui-tooltip-text {
  flex: 1;
}

.yj-ui-tooltip-arrow {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid #FACA14;
  /* 三角形的颜色 */
}
</style>