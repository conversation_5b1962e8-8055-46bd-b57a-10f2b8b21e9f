<template>
  <template v-if="!loading">
    <MyOrderPreviewScenic v-if="type == 'ticket'" :query="query"></MyOrderPreviewScenic>
    <MyOrderPreviewHotel v-else-if="type == 'hotel'" :query="query"></MyOrderPreviewHotel>
    <my-order-preview-package v-else-if="type === 'package'" :query="query"/>
  </template>
</template>

<script setup>
import {onLoad} from '@dcloudio/uni-app'

import {ref} from 'vue';
import {useUserStore} from '../../store/user';
import MyOrderPreviewPackage from "@/components/MyOrderPreviewPackage/MyOrderPreviewPackage.vue";
import {navTo} from "@/utils";
import {useGlobalStore} from "@/store/global";
import MyCustomerService from "@/components/MyCustomerService/MyCustomerService.vue";

const query = ref({})
const userStore = useUserStore()
const type = ref('')
const loading = ref(false)
const globalStore = useGlobalStore()

onLoad(q => {
  uni.setNavigationBarTitle({
    title: '完善订单信息',
  })

  loading.value = true
  if (userStore.token === '') {
    navTo('pages/login/login', {}, true)
    return
  }

  type.value = q.type || 'ticket'

  Object.assign(query.value, q)
  loading.value = false
})
</script>
