<template>
  <view class="chat-input-container">
    <!-- 主输入区域 -->
    <view :class="{ 'voice-ing': isVoiceIng }" class="chat-input-with-menu">
      <text :class="{
        'icon-yuyin3': !showVoice,
        'icon-shuru': showVoice && !isVoiceIng
      }" class="iconfont voice-icon" @tap="showVoice = !showVoice"></text>

      <view v-if="showVoice" class="voice" @touchstart.prevent="onTouchStart"
            @touchcancel.prevent="onTouchEnd" @touchend.prevent="onTouchEnd">
        <template v-if="isVoiceIng">
          <view class="lottie-container">
            <Loading v-if="isVoiceIng" class="speak-wave"/>
          </view>
        </template>
        <template v-else>
          按住说话
        </template>
      </view>
      <view v-else class="inner">
        <input v-model="text" :cursor-spacing="20" confirm-type="send" placeholder="说出您的旅行愿望。"
               @confirm="onTextSubmit"/>
      </view>

      <view class="right-buttons">
        <text v-if="!isVoiceIng && !showVoice" :class="{ active: canTextSend }" class="send iconfont icon-fasong3"
              @tap="onTextSubmit"></text>
        <view v-if="canShowMenu && !isVoiceIng && !showVoice" :class="{ 'menu-active': showMenu }" class="menu-trigger"
              @tap="toggleMenu">
          <text class="iconfont iconfont_menu icon-jia1"></text>
        </view>
      </view>
    </view>

    <!-- 菜单区域 -->
    <view v-if="showMenu" class="menu-area">
      <view class="menu-buttons">
        <view class="menu-item" @tap="onSearchScenic">
          <view class="menu-icon">
            <text class="iconfont iconfont_menu_pic icon-jindian"></text>
          </view>
          <view class="menu-text">搜景点</view>
        </view>
        <view class="menu-item" @tap="onSearchHotel">
          <view class="menu-icon">
            <text class="iconfont iconfont_menu_pic icon-jiudian"></text>
          </view>
          <view class="menu-text">搜酒店</view>
        </view>
        <!--        <view class="menu-item" @tap="onDiscover">-->
        <!--          <view class="menu-icon">-->
        <!--            <text class="iconfont iconfont_menu_pic icon-faxian1"></text>-->
        <!--          </view>-->
        <!--          <view class="menu-text">发现</view>-->
        <!--        </view>-->
        <view class="menu-item" @tap="onImportTrip">
          <view class="menu-icon">
            <text class="iconfont iconfont_menu_pic icon-lianjie"></text>
          </view>
          <view class="menu-text">导入行程</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, ref, toRaw, watch, onBeforeUnmount} from 'vue';
import Loading from '../Loading/Loading.vue';

const props = defineProps({
  reinit: {
    default: 0,
  },
  canShowMenu: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['done', 'search-scenic', 'search-hotel', 'discover', 'import-trip', 'showMenu'])

const text = ref('')
const showVoice = ref(false)
const isVoiceIng = ref(false)
const showMenu = ref(false)
const isRecordLocked = ref(false)

const canTextSend = computed(() => text.value.length > 0)
let recordManager

// #ifdef MP
recordManager = requirePlugin('WechatSI').getRecordRecognitionManager()
// #endif

watch(() => props.reinit, () => {
  //全局唯一对象，当多个页面使用时需要在页面初始化时将回调方法重写
  // #ifdef MP
  recordManager.onStart = function (res) {
    console.log('开始录音识别', res)
  }
  recordManager.onStop = function (res) {
    isVoiceIng.value = false
    const {result} = res

    setTimeout(() => {
      console.log('停止录音识别', res)
      emit('done', result)

      // 添加延时解锁机制，1.5秒后解锁录音功能
      setTimeout(() => {
        isRecordLocked.value = false
        console.log('录音功能已解锁')
      }, 1500)
    }, 10)
  }
  recordManager.onError = function (res) {
    console.log('录音识别出错', res)
    isVoiceIng.value = false

    // 发生错误时也需要解锁，但设置稍短的延时
    setTimeout(() => {
      isRecordLocked.value = false
      console.log('录音功能已解锁（错误恢复）')
    }, 1000)
  }
  // #endif

}, {immediate: true})

function onTouchStart() {
  // 如果录音被锁定，则不执行任何操作
  if (isRecordLocked.value) {
    uni.showToast({
      title: '录音准备中，请稍候',
      icon: 'none',
      duration: 1500
    })
    return
  }

  isVoiceIng.value = true
  // #ifdef MP
  recordManager.start()
  // #endif
}

function onTouchEnd() {
  // 如果没有正在录音，不执行任何操作（防止重复调用）
  if (!isVoiceIng.value) return

  isVoiceIng.value = false
  isRecordLocked.value = true // 锁定录音，防止快速重复触发

  // #ifdef MP
  recordManager.stop()
  // #endif
}

function onTextSubmit() {
  if (!canTextSend.value) {
    return // 未输入文字时不执行任何操作
  }

  const value = toRaw(text.value)
  text.value = ''
  emit('done', value)
}

function toggleMenu() {
  showMenu.value = !showMenu.value
  emit('showMenu', showMenu.value)
}

function onSearchScenic() {
  emit('search-scenic')
  //showMenu.value = false
}

function onSearchHotel() {
  emit('search-hotel')
  //showMenu.value = false
}

function onDiscover() {
  emit('discover')
  //showMenu.value = false
}

function onImportTrip() {
  emit('import-trip')
  //showMenu.value = false
}

// 添加组件卸载前的清理工作
onBeforeUnmount(() => {
  // 确保所有状态重置
  isVoiceIng.value = false
  isRecordLocked.value = false
  showVoice.value = false
  showMenu.value = false

  // 清理定时器等资源（如果有的话）
  console.log('聊天输入组件资源已清理')
})

</script>

<style lang="scss" scoped>
@import './MyChatInputWithMenu.scss';

.lottie-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 0;
  top: 0;
  z-index: 1;
}

.speak-wave {
  align-items: center;
  justify-content: center;
}

/* 覆盖scss文件中的样式 */
.voice-ing {
  background: #1890FF !important;
  color: white !important;
  position: relative;
}
</style>
