/* 图片裁剪组件样式 */

.image-cropper {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 400rpx;
    background-color: #ffffff;
}

/* 比例选择器样式 */
.aspect-ratio-selector {
    padding: 20rpx;
    background-color: #f8f9fa;
    border-bottom: 1rpx solid #e9ecef;
}

.ratio-buttons {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 10rpx;
}

.ratio-button {
    padding: 12rpx 24rpx;
    background-color: #ffffff;
    border: 2rpx solid #dee2e6;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #6c757d;
    text-align: center;
    transition: all 0.3s ease;
    min-width: 80rpx;
}

.ratio-button.active {
    background-color: #007bff;
    border-color: #007bff;
    color: #ffffff;
}

.ratio-button:active {
    transform: scale(0.95);
}

.cropper-container {
    flex: 1;
    width: 100%;
    min-height: 400rpx;
}

.uni-corpper {
    position: relative;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    box-sizing: border-box;
}

.uni-corpper-content {
    position: relative;
}

.uni-corpper-content image {
    display: block;
    width: 100%;
    min-width: 0 !important;
    max-width: none !important;
    height: 100%;
    min-height: 0 !important;
    max-height: none !important;
    image-orientation: 0deg !important;
    margin: 0 auto;
}

/* 移动图片效果 */
.uni-cropper-drag-box {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    cursor: move;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1;
}

/* 遮罩层样式 */
.crop-mask {
    position: absolute;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
    pointer-events: none;
}

/* 内部的信息 */
.uni-corpper-crop-box {
    position: absolute;
    background: transparent;
    z-index: 2;
}

.uni-corpper-crop-box .uni-cropper-view-box {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    overflow: visible;
    outline: 2rpx solid #ffffff;
    box-shadow: 0 0 0 1rpx rgba(0, 0, 0, 0.3);
}

/* 横向虚线 */
.uni-cropper-dashed-h {
    position: absolute;
    top: 33.33333333%;
    left: 0;
    width: 100%;
    height: 33.33333333%;
    border-top: 1rpx dashed rgba(255, 255, 255, 0.5);
    border-bottom: 1rpx dashed rgba(255, 255, 255, 0.5);
}

/* 纵向虚线 */
.uni-cropper-dashed-v {
    position: absolute;
    left: 33.33333333%;
    top: 0;
    width: 33.33333333%;
    height: 100%;
    border-left: 1rpx dashed rgba(255, 255, 255, 0.5);
    border-right: 1rpx dashed rgba(255, 255, 255, 0.5);
}

/* 四个方向的线  为了之后的拖动事件*/
.uni-cropper-line-t {
    position: absolute;
    display: block;
    width: 100%;
    background-color: #69f;
    top: 0;
    left: 0;
    height: 1rpx;
    opacity: 0.1;
    cursor: n-resize;
}

.uni-cropper-line-t::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0rpx;
    width: 100%;
    -webkit-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0);
    bottom: 0;
    height: 41rpx;
    background: transparent;
    z-index: 11;
}

.uni-cropper-line-r {
    position: absolute;
    display: block;
    background-color: #69f;
    top: 0;
    right: 0rpx;
    width: 1rpx;
    opacity: 0.1;
    height: 100%;
    cursor: e-resize;
}

.uni-cropper-line-r::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 41rpx;
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0);
    bottom: 0;
    height: 100%;
    background: transparent;
    z-index: 11;
}

.uni-cropper-line-b {
    position: absolute;
    display: block;
    width: 100%;
    background-color: #69f;
    bottom: 0;
    left: 0;
    height: 1rpx;
    opacity: 0.1;
    cursor: s-resize;
}

.uni-cropper-line-b::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0rpx;
    width: 100%;
    -webkit-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0);
    bottom: 0;
    height: 41rpx;
    background: transparent;
    z-index: 11;
}

.uni-cropper-line-l {
    position: absolute;
    display: block;
    background-color: #69f;
    top: 0;
    left: 0;
    width: 1rpx;
    opacity: 0.1;
    height: 100%;
    cursor: w-resize;
}

.uni-cropper-line-l::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 41rpx;
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0);
    bottom: 0;
    height: 100%;
    background: transparent;
    z-index: 11;
}

/* 边中间的扁平拖动条 */
.uni-cropper-drag-bar {
    background-color: #ffffff;
    border: 2rpx solid #333333;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    opacity: 1;
    position: absolute;
    z-index: 3;
}

/* 四个顶点的圆形控制点 */
.uni-cropper-corner-point {
    width: 30rpx;
    height: 30rpx;
    background-color: #ffffff;
    border: 2rpx solid #333333;
    border-radius: 50%;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    opacity: 1;
    position: absolute;
    z-index: 3;
}

/* 上边拖动条 */
.drag-bar-t {
    width: 50rpx;
    height: 12rpx;
    top: -8rpx;
    left: 50%;
    margin-left: -25rpx;
    cursor: n-resize;
}

/* 右边拖动条 */
.drag-bar-r {
    width: 12rpx;
    height: 50rpx;
    top: 50%;
    left: 100%;
    margin-left: -4rpx;
    margin-top: -25rpx;
    cursor: e-resize;
}

/* 下边拖动条 */
.drag-bar-b {
    width: 50rpx;
    height: 12rpx;
    left: 50%;
    top: 100%;
    margin-left: -25rpx;
    margin-top: -4rpx;
    cursor: s-resize;
}

/* 左边拖动条 */
.drag-bar-l {
    width: 12rpx;
    height: 50rpx;
    left: 0%;
    top: 50%;
    margin-left: -8rpx;
    margin-top: -25rpx;
    cursor: w-resize;
}

/* 右上角顶点 */
.point-tr {
    top: -15rpx;
    left: 100%;
    margin-left: -15rpx;
    cursor: ne-resize;
}

/* 右下角顶点 */
.point-rb {
    left: 100%;
    top: 100%;
    margin-left: -15rpx;
    margin-top: -15rpx;
    cursor: se-resize;
}

/* 左下角顶点 */
.point-bl {
    left: 0%;
    top: 100%;
    margin-left: -15rpx;
    margin-top: -15rpx;
    cursor: sw-resize;
}

/* 左上角顶点 */
.point-lt {
    left: 0%;
    top: 0%;
    margin-left: -15rpx;
    margin-top: -15rpx;
    cursor: nw-resize;
}

/* 裁剪框预览内容 */
.uni-cropper-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.uni-cropper-viewer image {
    position: absolute;
    z-index: 2;
}

/* 上传图片区域样式 */
.upload-area {
    width: 100%;
    height: 400rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx;
    border: 4rpx dashed #cccccc;
    border-radius: 20rpx;
    background-color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-content:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

.upload-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    color: #999999;
}

.upload-text {
    font-size: 32rpx;
    color: #666666;
    text-align: center;
}

/* 底部按钮区域样式 - 调整为非固定定位 */
.bottom-buttons {
    width: 100%;
    background-color: #ffffff;
    border-top: 1rpx solid #eeeeee;
    padding: 30rpx;
    margin-top: 20rpx;
}

.button-row {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
}

.button-item {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f5f5f5;
    color: #333333;
}

.button-item:hover {
    opacity: 0.8;
}

.button-item.primary {
    background-color: #1890ff;
    color: #ffffff;
}

.button-item.primary:hover {
    background-color: #40a9ff;
}
