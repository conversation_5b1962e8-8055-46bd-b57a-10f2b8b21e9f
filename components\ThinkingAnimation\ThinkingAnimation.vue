<template>
	<view class="thinking-container" :style="{ width: componentSize, height: componentSize }">
		<view class="shape shape1" :class="{ 'animated': animateShapes }"></view>
		<view class="shape shape2" :class="{ 'animated': animateShapes }"></view>
		<view class="center-area">
			<text v-if="!showImage" class="text" :style="{ fontSize: textSize }">思考中</text>
			<image
				v-if="showImage && imageSrc"
				:src="imageSrc"
				class="center-image"
				mode="aspectFill"
			></image>
		</view>
	</view>
</template>

<script>
	export default {
		name: "ThinkingAnimation", // Component name
		props: {
			// Prop to control image visibility
			showImage: {
				type: <PERSON>olean,
				default: false
			},
			// Prop for the image source URL
			imageSrc: {
				type: String,
				default: ''
			},
			// Prop to control the overall size of the component (e.g., '72px', '100rpx')
			size: {
				type: String,
				default: '300rpx' // Default size if not provided
			},
            // Prop to control whether the outer shapes animate
            animateShapes: {
                type: <PERSON><PERSON><PERSON>,
                default: true // Default is to animate
            }
		},
		computed: {
			// Computed property to easily use the size prop in the template style
			componentSize() {
				return this.size;
			},
			// Computed property to calculate text size relative to component size
			textSize() {
				const match = this.size.match(/(\d+)(\w+)/);
				if (match) {
					const value = parseFloat(match[1]);
					// Adjust the multiplier (0.09 here) for desired text size
					// Use rpx as the base unit for calculation consistency in uni-app
					return `${Math.max(12, Math.floor(value * 0.09))}rpx`;
				}
				return '28rpx'; // Fallback default size
			},
            // Removed shape1Style and shape2Style computed properties
		},
		data() {
			return {};
		}
	}
</script>

<style scoped lang="scss">
	/* Main container for the animation */
	.thinking-container {
		position: relative;
		/* width and height are now set dynamically via :style */
		display: flex;
		justify-content: center;
		align-items: center;
		margin: auto; /* Helps centering if parent is flex */
		box-sizing: border-box; /* Include padding/border in element's total width/height */
	}

	/* Base style for the rotating shapes */
	.shape {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 45%;
		/* animation properties are now applied via class */
		transform-origin: center center;
		opacity: 0.85;
		background-size: cover;
	}

	/* Style for the first shape */
	.shape1 {
		background: linear-gradient(45deg, #30cfd0 0%, #330867 100%);
	}
    /* Apply animation only when 'animated' class is present */
    .shape1.animated {
        animation: rotate-and-hue 8s linear infinite;
    }


	/* Style for the second shape */
	.shape2 {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	}
    /* Apply animation only when 'animated' class is present */
    .shape2.animated {
         animation: rotate-reverse-and-hue 12s linear infinite;
    }

	/* The white center area */
	.center-area {
		position: relative;
		width: 75%; /* Relative to container */
		height: 75%; /* Relative to container */
		background-color: white;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1;
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
		overflow: hidden;
		box-sizing: border-box;
	}

	/* Style for the text */
	.text {
		/* font-size is now set dynamically via :style */
		color: #333;
		font-weight: bold;
		white-space: nowrap;
		/* Text scaling animation remains */
		animation: scaleText 2.5s ease-in-out infinite;
	}

	/* Style for the center image */
	.center-image {
		width: 90%;
		height: 90%;
		border-radius: 50%;
		object-fit: cover;
		display: block;
	}


	/* Keyframes remain the same */
	@keyframes rotate-and-hue {
		from { transform: rotate(0deg); filter: hue-rotate(0deg); }
		to { transform: rotate(360deg); filter: hue-rotate(360deg); }
	}
	@keyframes rotate-reverse-and-hue {
		from { transform: rotate(360deg); filter: hue-rotate(360deg); }
		to { transform: rotate(0deg); filter: hue-rotate(0deg); }
	}
	@keyframes scaleText {
		0%, 100% { transform: scale(1); }
		50% { transform: scale(1.1); }
	}
</style>