// 定义一个月的秒数，一个月按 30 天算，一天 24 小时，一小时 60 分钟，一分钟 60 秒
const ONE_MONTH_IN_SECONDS = 7 * 24 * 60 * 60;

/**
 * 设置缓存
 * @param {string} key - 缓存的键
 * @param {any} value - 缓存的值
 * @param {number} [expiration=ONE_MONTH_IN_SECONDS] - 缓存的过期时间（秒），默认为一个月
 */
function cacheSet(key, value, expiration = ONE_MONTH_IN_SECONDS) {
  const cacheItem = {
    value,
    expiration: Date.now() + expiration * 1000
  };
  uni.setStorage({
    key,
    data: cacheItem,
  })
}

/**
 * 获取缓存
 * @param {string} key - 缓存的键
 * @returns {any|null} - 如果缓存未过期则返回缓存的值，否则返回 null
 */
function cacheGet(key) {
  const cacheItem = uni.getStorageSync(key);
  if (cacheItem) {
    if (Date.now() < cacheItem.expiration) {
      return cacheItem.value;
    } else {
      // 缓存已过期，移除缓存
      uni.removeStorageSync(key);
    }
  }
  return null;
}

/**
 * 异步来获取缓存
 * @param key
 * @returns {Promise<unknown>}
 */
function cacheGetAsync(key) {
  return new Promise((resolve, reject) => {
    uni.getStorage({
      key,
      success: (res) => {
        const cacheItem = res.data;
        if (cacheItem) {
          if (Date.now() < cacheItem.expiration) {
            resolve(cacheItem.value);
          } else {
            // 缓存已过期，移除缓存
            uni.removeStorageSync(key);
            resolve(null);
          }
        }
      },
      fail: (err) => {
        resolve(null);
      }
    })
  })
}

/**
 * 移除缓存
 * @param {string} key - 缓存的键
 */
function cacheRemove(key) {
  uni.removeStorageSync(key);
}

/**
 * 清除所有缓存
 */
function cacheClear() {
  uni.clearStorageSync();
}

export {
  cacheSet,
  cacheGet,
  cacheRemove,
  cacheClear
};
