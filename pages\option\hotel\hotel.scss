@import '../../../styles/_mix.scss';
@import '../../../styles/_define.scss';

.container {
  // min-height: 100vh;
  background: $page-bg-color;
  padding-bottom: 120rpx;
}

.header {
  padding: 15rpx $padding-page;
  background: white;

  >view {
    @include center();
    gap: $padding-small;
  }

  >view {
    &:first-child {
      .iconfont {
        font-weight: bold;
      }

      .input {
        flex: 1;
      }
    }
  }

  .zones {
    //margin: 0 $padding-page;
    margin-top: $padding-small;
    justify-content: space-between;
    position: relative;

    scroll-view {
      flex: 1;
      white-space: nowrap;
      //width: 82%;

      .zone-list {
        @include center();
        justify-content: flex-start;
        gap: $padding-small;

        view {
          border: 1rpx solid $border-color-v2;
          color: $font-color-gray;
          padding: $padding-small 36rpx;
          border-radius: $border-radius-max;

          &.active {
            background: #E7F4FF;
            color: $plan-color-3;
            border-color: $plan-color-3;
          }
        }
      }
    }
  }


}

.list {
  display: flex;
  flex-direction: column;
  gap: $padding-v2;
  margin-top: $padding-v2;

  .list-item {
    background: white;
    @include center();
    justify-content: flex-start;
    padding: $padding-v2;
    gap: $padding-small;

    .pic {
      width: 140rpx;
      height: 140rpx;

      image {
        border-radius: $border-radius-v2;
      }
    }

    .right {
      flex: 1;

      @include center();
      justify-content: space-between;

      >view {
        &:first-child {
          flex: 1;
        }
      }

      .name {
        @include ellipse();
        @include center();
        justify-content: flex-start;
        font-weight: 400;
        width: 400rpx;
      }

      .location {
        font-size: $fontsize-mini;
        color: $font-color-gray;
      }

      .tag {
        font-size: $fontsize-mini;
      }

      .join {
        border: 1rpx solid $border-color-v2;
      }

    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: $box-shadow;
  border-radius: $border-radius-v2;
  padding: $padding-page;

  .actions,
  .actions .left,
  .selected {
    @include center();
  }

  .selected {
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: $padding-small;
    font-size: $fontsize-mini;
    margin: $padding-small 0;

    .iconfont {
      font-size: $fontsize-mini;
      color: $plan-color-3;
    }
  }

  .actions {

    justify-content: space-between;

    .left {
      background: #EDEEF0;
      border-radius: 37rpx;
      padding: $padding-v2 41rpx;

      :deep(.uni-select) {
        border: 0;
        //padding: 0;
        font-size: $fontsize;
        border-radius: 0;
        height: auto;

        .uni-select__input-box {
          font-size: $fontsize;
          height: auto;
          font-weight: 500;
        }
      }
    }
  }
}