<script setup>
import MyInfoPopup from '@/components/MyInfoPopup/MyInfoPopup.vue';
import MyInputNumber from '@/components/MyInputNumber/MyInputNumber.vue';
import {formatMoney} from '@/utils';
import {computed} from 'vue';

const num = defineModel()
const props = defineProps({
  sku: {default: () => ({})},
  price: {default: 0},//单价
})
const emits = defineEmits(['close', 'confirm'])
const totalPrice = computed(() => {
  return num.value * props.price
})
</script>

<template>
  <MyInfoPopup class="pop" title="选择购买数量" type="bottom" @close="emits('close')">
    <view class="wrap">
      <view v-if="sku.adult_num" class="lines">
        <text class="left">成人数</text>
        <text class="right">{{ sku.adult_num }}</text>
      </view>
      <view v-if="sku.children_num" class="lines">
        <text class="left">儿童数</text>
        <text class="right">{{ sku.children_num }}</text>
      </view>
      <view class="lines">
        <text class="left">购买数量</text>
        <view class="right">
          <MyInputNumber v-model="num" :max="sku.left_stock" :min="1"/>
        </view>
      </view>
      <text class="button" @tap="emits('confirm')">
        总计{{ formatMoney(totalPrice) }}立即购买
      </text>
    </view>
  </MyInfoPopup>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

.pop {
  :deep(.bottom) {
    .main {
      background: white;

      .popup-content {
        padding-left: 0;
        padding-right: 0;
      }
    }

  }
}

.wrap {
  @include center(column);

  .lines {
    @include center();
    justify-content: space-between;
    padding: 32rpx 76rpx 30rpx 78rpx;
    width: 100%;

    &:nth-child(2n+1) {
      background: #F7F7F9;
    }

    &:nth-child(2n) {
      background: white;
    }

    .left {
      font-size: 28rpx;
      color: $black-color;
    }

    .right {
      width: 194rpx;
      @include center();
    }

  }

  .button {
    margin-top: 50rpx;
    margin-bottom: 44rpx;
    background: #1890FF;
    border-radius: 48rpx;
    width: 634rpx;
    height: 72rpx;
    @include center();
    font-weight: bold;
    font-size: 28rpx;
    color: white;
  }
}
</style>