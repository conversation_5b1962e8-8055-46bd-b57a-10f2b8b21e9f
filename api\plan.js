import request from "../utils/request.js";

export function planPoster(plan_id) {
  return request({
    method: "get",
    url: "/api/v1/front/plan/poster",
    data: { plan_id, return_url: 1 },
  });
}

export function planUpdate(data) {
  return request({
    method: "post",
    url: "/api/v1/front/plan/update",
    data,
  });
}

export function planHistoryRemove(id, ai_reqid = "") {
  return request({
    method: "post",
    url: "/api/v1/front/plan/history_del",
    data: { history_id: id, ai_reqid },
  });
}

export function planHistory(data) {
  return request({
    method: "get",
    url: "/api/v1/front/plan/history",
    data,
  });
}

export function planRelateTuan(data) {
  return request({
    method: "get",
    url: "/api/v1/front/plan/relate_tuan",
    data,
  });
}

export function planCovers(data) {
  return request({
    method: "get",
    url: "/api/v1/front/plan/covers",
    data,
  });
}

export function planDetail(data, showLoading = false) {
  return request(
    {
      method: "get",
      url: "/api/v1/front/plan/detail",
      data,
    },
    showLoading,
    false
  );
}

export function planLike(data, loading = true) {
  return request(
    {
      url: "/api/v1/front/plan/like",
      method: "post",
      data,
    },
    loading
  );
}

export function planPrompt(data, loading = false) {
  return request(
    {
      url: "/api/v1/front/plan/prompt",
      method: "post",
      data,
    },
    loading,
    false
  );
}

export function planSubmit(data, loading = false) {
  return request(
    {
      url: "/api/v1/front/plan/submit",
      method: "post",
      data,
    },
    loading
  );
}

export function planOption() {
  return request({
    method: "get",
    url: "/api/v1/front/option",
  });
}

export function planPdf(plan_id) {
  return request(
    {
      method: "post",
      url: "/api/v1/front/plan/pdf",
      data: { plan_id },
    },
    false,
    false
  );
}

export function planCostDetail(data) {
  return request({
    method: "get",
    url: "/api/v1/front/plan/costdetail",
    data
  });
}
