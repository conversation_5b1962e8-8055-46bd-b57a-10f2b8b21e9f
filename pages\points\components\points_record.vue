<script setup>
import { ref, onMounted, defineExpose, getCurrentInstance } from "vue";
import { logList } from "@/api/point.js";

// 时间过滤选项
const timeOptions = ref(['全部时间', '最近一年', '最近半年'])
const selectedTimeOption = ref('全部时间')
const showFilterDropdown = ref(false)

// 分页参数
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loading = ref(false)
const total = ref(0)

// 接口数据
const records = ref([])
const currentBalance = ref(0)

// 动态高度计算
const scrollViewHeight = ref('400px')
const instance = getCurrentInstance()

// 时间参数
const timeParams = ref({
  start: 0,
  end: 0
})

// 获取积分记录列表
const fetchRecords = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true

  try {
    // 构建请求参数
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...timeParams.value
    }

    // 调用接口
    const res = await logList(params)

    if (res.code === 0) {
      const data = res.data

      // 更新总数
      total.value = data.total

      // 更新数据列表
      if (isLoadMore) {
        records.value = [...records.value, ...data.list]
      } else {
        records.value = data.list
      }

      // 判断是否还有更多数据
      hasMore.value = records.value.length < total.value

      if (hasMore.value) {
        page.value++
      }
    } else {
      uni.showToast({
        title: res.msg || '获取记录失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取记录失败', error)
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 重置并刷新数据
const refreshList = () => {
  page.value = 1
  hasMore.value = true
  fetchRecords()
}

// 计算 scroll-view 高度
const calculateScrollHeight = () => {
  const query = uni.createSelectorQuery().in(instance.proxy)

  // 获取系统信息
  uni.getSystemInfo({
    success: (systemInfo) => {
      const screenHeight = systemInfo.screenHeight

      // 获取固定头部的高度
      query.select('.fixed-header').boundingClientRect((headerRect) => {
        if (headerRect) {
          // 计算可用高度：屏幕高度 - 导航栏高度 - 固定头部高度 - 安全边距
          const availableHeight = screenHeight - (headerRect.top + headerRect.height) - 20
          scrollViewHeight.value = `${availableHeight}px`
        } else {
          // 如果无法获取头部高度，使用默认计算
          scrollViewHeight.value = `${screenHeight - 200}px`
        }
      }).exec()
    }
  })
}

// 初始化
onMounted(() => {
  refreshList()
  // 延迟计算高度，确保 DOM 已渲染
  setTimeout(calculateScrollHeight, 100)
})

// 切换时间选项
const changeTimeOption = (option) => {
  selectedTimeOption.value = option
  showFilterDropdown.value = false

  // 根据选择的时间筛选记录
  updateTimeParams(option)
  refreshList()
}

// 根据选择的时间选项更新时间参数
const updateTimeParams = (option) => {
  const now = Math.floor(Date.now() / 1000) // 当前时间的秒级时间戳

  switch (option) {
    case '最近一年':
      // 一年前的时间戳
      const oneYearAgo = new Date()
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
      timeParams.value = {
        start: Math.floor(oneYearAgo.getTime() / 1000),
        end: now
      }
      break
    case '最近半年':
      // 半年前的时间戳
      const sixMonthsAgo = new Date()
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
      timeParams.value = {
        start: Math.floor(sixMonthsAgo.getTime() / 1000),
        end: now
      }
      break
    default:
      // 全部时间，不设置时间范围
      timeParams.value = {
        start: 0,
        end: 0
      }
  }
}

// 加载更多数据
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  fetchRecords(true)
}

// 页面触底事件处理函数
const onReachBottom = () => {
  loadMore()
}

// 获取记录金额样式
const getAmountClass = (isPlus) => {
  return isPlus === 1 ? 'income' : 'expense'
}

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000) // 将秒转为毫秒
  if (isNaN(date.getTime())) return ''
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 向父组件暴露方法
defineExpose({
  onReachBottom
})
</script>

<template>
  <view class="record-page">
    <!-- 固定顶部部分 -->
    <view class="fixed-header">
      <!-- 时间选择器卡片 -->
      <view class="time-filter-card">
        <view class="filter-content" @tap="showFilterDropdown = !showFilterDropdown">
          <text class="selected-option">{{ selectedTimeOption }}</text>
          <image class="dropdown-icon" src="https://rp.yjsoft.com.cn/yiban/static/icons/arrow_down.png"
            mode="aspectFit"></image>
        </view>

        <!-- 下拉选项 -->
        <view class="dropdown-options" v-if="showFilterDropdown">
          <view v-for="option in timeOptions" :key="option" class="option-item"
            :class="{ active: selectedTimeOption === option }" @tap="changeTimeOption(option)">
            {{ option }}
          </view>
        </view>
      </view>
    </view>

    <!-- 可滚动的记录列表 -->
    <scroll-view class="record-container" :style="{ height: scrollViewHeight }" scroll-y="true"
      :enable-back-to-top="true" enhanced :show-scrollbar="false" :scroll-with-animation="true"
      @scrolltolower="loadMore" :lower-threshold="50">
      <view class="records-list">
        <view v-for="record in records" :key="record.id" class="record-item">
          <!-- 记录图标 -->
          <view class="record-icon">
            <image :src="record.icon || 'https://rp.yjsoft.com.cn/yiban/static/points/default_icon.png'"
              mode="aspectFit"></image>
          </view>

          <!-- 记录内容 -->
          <view class="record-content">
            <view class="record-info">
              <view class="record-title">{{ record.remark || record.sub_type_text }}</view>
              <view class="record-date">{{ formatDateTime(record.created_at) }}</view>
              <!-- 购买套餐类型的记录显示过期时间 -->
              <view class="record-expire" v-if="record.sub_type === 1 && record.package_expire_at">
                有效期至：{{ formatDateTime(record.package_expire_at).split(' ')[0] }}
              </view>
            </view>

            <view class="record-amount" :class="getAmountClass(record.is_plus)">
              {{ record.is_plus === 1 ? '+' + record.change_amount : '-' + record.change_amount }}
            </view>
          </view>
        </view>

        <!-- 加载更多/无更多数据提示 -->
        <view class="load-more" v-if="records.length > 0">
          <text v-if="loading">加载中...</text>
          <text v-else-if="!hasMore">没有更多数据了</text>
        </view>

        <!-- 无数据提示 -->
        <view class="no-data" v-if="records.length === 0 && !loading">
          <text>暂无积分记录</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss">
@import "./points_record.scss";
</style>