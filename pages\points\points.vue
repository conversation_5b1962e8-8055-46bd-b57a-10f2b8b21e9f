<script setup>
import { computed, ref, watch } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import PointsPurchase from "./components/points_purchase.vue";
import PointsRecord from "./components/points_record.vue";
import { navTo, showToast, getCdnUrl } from "@/utils";
import { PayTypePackage } from "@/utils/constmap";
import { useGlobalStore } from "@/store/global";
import { isProductionIOS } from "@/utils/device";

const globalStore = useGlobalStore()
const currentTab = ref(0)
const swiperInstance = ref(null)
const navBarHeight = ref(0)
const pointsRecordRef = ref(null)

// 弹窗相关状态
const showConfirmModal = ref(false)
const agreementChecked = ref(false)
const selectedPackage = ref(null)
const needShowPackage = ref(false)

const tabs = computed(() => {
  const ret = [
    {
      id: 1,
      name: '积分记录',
      icon: getCdnUrl('/static/points/jfjl_unselected.png'),
      iconSelected: getCdnUrl('/static/points/jfjl_selected.png')
    }
  ]

  if (needShowPackage.value) {
    ret.unshift({
      id: 0,
      name: '积分购买',
      icon: getCdnUrl('/static/points/points_unselected.png'),
      iconSelected: getCdnUrl('/static/points/points_selected.png')
    })
  }

  return ret
})

const onNavBarLoad = (data) => {
  navBarHeight.value = `${data.height}px`
}

const onTabClick = (index) => {
  currentTab.value = index
}

const onSwiperChange = (e) => {
  currentTab.value = e.detail.current
}

// 处理套餐选择事件
const onSelectPackage = async (pkg) => {
  selectedPackage.value = pkg
  agreementChecked.value = false // 重置勾选状态
  showConfirmModal.value = true
}

// 切换协议勾选状态
const toggleAgreement = () => {
  agreementChecked.value = !agreementChecked.value
}

// 确认购买
const confirmPurchase = () => {
  if (!agreementChecked.value) {
    showToast('需要阅读并勾选同意服务须知')
    return
  }

  navTo('pages/pay/pay', {
    package_id: selectedPackage.value.id,
    pay_type: PayTypePackage,
  })

  showConfirmModal.value = false
}

// 关闭弹窗
const closeModal = () => {
  showConfirmModal.value = false
  selectedPackage.value = null
}

// 查看服务须知
const viewServiceAgreement = () => {
  navTo('pages/h5/h5', { url: globalStore.serviceUrl })
}

// 监听currentTab变化，同步swiper
watch(currentTab, (newIndex) => {
  // 通过swiper实例切换到对应页面
  if (swiperInstance.value) {
    // 注意：这里可能需要根据实际情况调整swiper的切换方法
  }
})

// 页面触底事件
function onReachBottom() {
  // 当前在积分记录页时，调用组件的触底方法
  if (currentTab.value === 1 && pointsRecordRef.value) {
    pointsRecordRef.value.onReachBottom()
  }
}

// 页面加载时处理参数
onLoad(async (query = {}) => {
  needShowPackage.value = !await isProductionIOS()

  // 获取 tab 参数，支持数值型（0 或 1）
  const tabParam = query.tab

  if (tabParam !== undefined) {
    // 将参数转换为数字
    const tabIndex = Number(tabParam)

    // 验证参数是否在有效范围内（0-1）
    if (Number.isInteger(tabIndex) && tabIndex >= 0 && tabIndex < tabs.value.length) {
      currentTab.value = tabIndex
    }
    // 如果参数无效，保持默认值 0，不做任何操作
  }
  // 如果没有 tab 参数，保持默认值 0
})

// 暴露页面生命周期钩子
defineExpose({
  onReachBottom
})
</script>

<template>
  <view class="points-page">
    <!-- 导航栏 -->
    <YjNavBar :custom-style="{ backgroundColor: 'transparent' }" @load="onNavBarLoad"
      @not-found="navTo('pages/index/index')">
      <template #title>
        <!-- Tab栏 -->
        <view class="tab-container">
          <view v-for="(tab, index) in tabs" :key="tab.id" :class="{ active: currentTab === index }" class="tab-item"
            @tap="onTabClick(index)">
            <image :src="currentTab === index ? tab.iconSelected : tab.icon" class="tab-icon" mode="aspectFit" />
            <text class="tab-text">{{ tab.name }}</text>
            <view v-if="currentTab === index" class="tab-indicator"></view>
          </view>
        </view>
      </template>
    </YjNavBar>

    <!-- 内容区域 -->
    <swiper ref="swiperInstance" :current="currentTab" :style="{ 'padding-top': navBarHeight }" class="swiper-container"
      @change="onSwiperChange">
      <swiper-item v-if="needShowPackage">
        <PointsPurchase @selectPackage="onSelectPackage" />
      </swiper-item>
      <swiper-item>
        <PointsRecord ref="pointsRecordRef" />
      </swiper-item>
    </swiper>

    <!-- 确认购买弹窗 -->
    <view v-if="showConfirmModal" class="modal-mask" @tap="closeModal">
      <view class="confirm-modal" @tap.stop>
        <view class="modal-content">
          <view class="agreement-section">
            <view class="checkbox-wrapper" @tap="toggleAgreement">
              <view :class="{ checked: agreementChecked }" class="checkbox"></view>
            </view>
            <view class="agreement-text">
              <text @tap="toggleAgreement">我已阅读并同意</text>
              <text class="service-link" @tap.stop="viewServiceAgreement">《服务须知》</text>
            </view>
          </view>

          <button :class="{ disabled: !agreementChecked }" class="confirm-btn" @tap="confirmPurchase">
            确认购买
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
@import "./points.scss";
</style>
