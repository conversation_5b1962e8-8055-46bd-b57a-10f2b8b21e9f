/**
 * 检查当前设备是否为 iOS 设备
 * @returns {Promise<boolean>} 如果是 iOS 设备返回 true，否则返回 false
 */
export function isIOSDevice() {
  return new Promise((resolve, reject) => {
    try {
      uni.getSystemInfo({
        success: (res) => {
          // 判断平台标识，iOS 设备会返回 'ios'
          const isIOS = res.platform === 'ios';
          
          console.log('当前设备是否为 iOS 设备:', res.platform);
          
          resolve(isIOS);
        },
        fail: (err) => {
          console.error('获取设备信息失败:', err);
          reject(err);
        }
      });
    } catch (error) {
      console.error('检测 iOS 设备时发生错误:', error);
      reject(error);
    }
  });
}

/**
 * 检查当前是否为生产环境
 * @returns {boolean} 如果是生产环境返回 true，否则返回 false
 */
export function isProductionEnvironment() {
  return process.env.NODE_ENV === 'production';
}

/**
 * 检查当前是否为生产环境的iOS设备
 * @returns {Promise<boolean>} 如果是生产环境的iOS设备返回 true，否则返回 false
 */
export async function isProductionIOS() {
  try {
    // 检查是否是生产环境
    const isProduction = isProductionEnvironment();
    
    // 如果不是生产环境，直接返回false
    if (!isProduction) {
      return false;
    }
    
    // 检查是否是iOS设备
    const isIOS = await isIOSDevice();
    
    // 返回组合结果
    return isProduction && isIOS;
  } catch (error) {
    console.error('检测生产环境iOS设备时发生错误:', error);
    return false;
  }
}