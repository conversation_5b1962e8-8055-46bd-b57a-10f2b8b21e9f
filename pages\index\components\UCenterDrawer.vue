<script setup>

import { computed, onMounted, ref, watch } from "vue";
import { UserTypeNormal } from "@/utils/constmap";
import { usercenter } from "@/api";
import { copyText, getCdnUrl, navTo } from "@/utils";
import { chatList } from "@/api/chat";
import { uniGetMenuButtonBounding, uniGetSystemInfo } from "@/utils/uni";

const show = defineModel({ type: Boolean, default: false })
const props = defineProps({
  reload: { type: Number, default: 0 },
})
const mineRef = ref()
const userInfo = ref({
  id: '00000',
  avatar: '',
  invite_code: '',
  wifi_card_rel_id: 0,
  vip_info: {
    user_type: UserTypeNormal
  },
  vip_conf: {},
  package_info: {
    amount: 0,
    package_id: 0,
    package_name: '',
    expire_at: ''
  }
})

const cmpDrawWidth = async () => {
  try {
    const [{ screenWidth }, { left }] = await Promise.all([
      uniGetSystemInfo(),
      uniGetMenuButtonBounding(),
    ])
    drawWidth.value = screenWidth * .8
    let leftPad = 10
    if (left > 0 && drawWidth.value > left - leftPad) {
      drawWidth.value = left - leftPad
    }
  } catch (e) {
  }

  if (drawWidth.value > maxDrawWith) {
    drawWidth.value = maxDrawWith
  }
}

const pointsAmount = computed(() => {
  const amount = userInfo.value.package_info?.amount || 0
  // 自定义千分位格式化函数
  return amount.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
})
const hasPackage = computed(() => {
  return (userInfo.value.package_info?.package_id || 0) > 0
})
const packageTitle = computed(() => {
  return hasPackage.value ? `${userInfo.value.package_info?.package_name || ''}` : '积分购买'
})
const formattedExpireDate = computed(() => {
  if (!hasPackage.value || !userInfo.value.package_info?.expire_at) {
    return ''
  }
  const date = new Date(userInfo.value.package_info.expire_at * 1000)
  if (isNaN(date.getTime())) {
    return ''
  }
  return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`
})
const chats = ref([])

const maxDrawWith = uni.rpx2px(648)
const drawWidth = ref(maxDrawWith) // 固定抽屉宽度

const doChange = (e) => {
  show.value = e
}

const doLoad = async () => {
  try {
    const { data } = await usercenter()
    Object.assign(userInfo.value, data)
  } catch (e) {
    console.error('ucenter doLoad', e)
  }
  const { data } = await chatList()
  chats.value = data.list
}

const toOrder = (type = 'all') => {
  navTo('pages/orders/orders', { type })
}

const toPoints = (index) => {
  doChange(false)
  navTo('pages/points/points?tab=' + index)
}

const watchShow = (val) => {
  if (val) {
    doLoad()
  }
  mineRef.value?.[val ? 'open' : 'close']()
}

function onCopyId() {
  copyText(String(userInfo.value.id), true)
}

onMounted(() => {
  watchShow(show.value)
  cmpDrawWidth()
})

watch(show, watchShow, { immediate: true })
watch(() => props.reload, () => {
  watchShow(show.value)
})

</script>

<template>
  <view v-if="drawWidth > 0">
    <uni-drawer ref="mineRef" :width="drawWidth" class="wrap" maskClick mode="left" @change="doChange">
      <scroll-view :show-scrollbar="false" enhanced scroll-y>
        <view class="ucenter">
          <!-- 渐变背景区域 -->
          <view class="gradient-section">
            <view class="utop" @tap="!userInfo.nickname && navTo('pages/login/login')">
              <view class="icon">
                <image :src="`${userInfo.avatar || getCdnUrl('/static/mine/user.png')}`" mode="aspectFit"></image>
              </view>
              <view class="nickname">
                <template v-if="userInfo.nickname">
                  <view class="nickname-text">
                    {{ userInfo.nickname }}

                  </view>
                  <view class="id-line">
                    <text>ID: {{ userInfo.id }}</text>
                    <text class="iconfont icon-fuzhi" @tap="onCopyId"></text>
                  </view>
                </template>
                <view v-else>注册/登录</view>
              </view>
            </view>
            <view class="order-list">
              <view class="accordion-item" @tap="toOrder()">
                <text class="item-text">全部订单</text>
              </view>
              <view class="accordion-item" @tap="toOrder('not')">
                <text class="item-text">待付款</text>
              </view>
              <view class="accordion-item" @tap="toOrder('pay')">
                <text class="item-text">待出行</text>
              </view>
              <view class="accordion-item" @tap="toOrder('refund')">
                <text class="item-text">售后</text>
              </view>
            </view>
          </view>
          <view class="card-left" @tap="toPoints(0)">
            <view class="left">
              <template v-if="hasPackage">
                <text class="package-title">{{ packageTitle }}</text>
                <text>有效期：{{ formattedExpireDate }}</text>
              </template>
              <template v-else>
                <text class="package-title">{{ packageTitle }}</text>
              </template>

            </view>
            <view class="right">
              <view class="points-text">
                {{ pointsAmount }}
                <text class="iconfont icon-pinpai" />
              </view>
              <view class="renew">续期</view>
            </view>
          </view>

          <view class="accordion">
            <view class="accordion-item" @tap="navTo('pages/mine/plans/plans')">
              <image :src="getCdnUrl('/static/mine/icon-plan.png')" class="item-icon" mode="aspectFill" />
              <text class="item-text">行程收藏</text>
              <text class="item-right iconfont icon-gengduo light"></text>
            </view>
            <view class="accordion-item" @tap="navTo('pages/peoples/peoples')">
              <image :src="getCdnUrl('/static/mine/icon-people.png')" class="item-icon" mode="aspectFill" />
              <text class="item-text">常用信息</text>
              <text class="item-right iconfont icon-gengduo light"></text>
            </view>
            <view class="accordion-item" @tap="navTo('pages/mine/wifis/wifis')">
              <image :src="getCdnUrl('/static/mine/icon-wifi.png')" class="item-icon" mode="aspectFill" />
              <text class="item-text">机上WIFI</text>
              <text class="item-right iconfont icon-gengduo light"></text>
            </view>
            <view class="accordion-item" @tap="navTo('pages/mine/myactivities/myactivities')">
              <image :src="getCdnUrl('/static/home/<USER>/my_act.png')" class="item-icon" mode="aspectFill" />
              <text class="item-text">我的活动</text>
              <text class="item-right iconfont icon-gengduo light"></text>
            </view>
            <view class="accordion-item" @tap="navTo('pages/mine/invitecode')">
              <image :src="getCdnUrl('/static/mine/icon-invitecode.png')" class="item-icon" mode="aspectFill" />
              <text class="item-text">邀请码</text>
              <text class="item-right iconfont icon-gengduo light"></text>
            </view>
          </view>

          <view class="space" />

          <view class="chat-list">
            <view class="label">对话记录:</view>
            <view v-if="!chats.length" class="empty">暂无对话</view>
            <view v-for="(item, i) in chats" v-else :key="i" class="chat-item"
              @tap="doChange(false) || navTo('pages/chat/chat', { context_id: item.context_id })">{{ item.title }}
            </view>
          </view>

        </view>
      </scroll-view>
    </uni-drawer>
  </view>
</template>

<style lang="scss" scoped>
@import "ucenter";
</style>
