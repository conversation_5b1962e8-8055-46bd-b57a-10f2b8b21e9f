@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';
@import '../../styles/tour.scss';

.app-container {
  position: relative;
  padding-bottom: 148rpx;
  background-color: $page-bg-color;
}

:deep(.navbar), .app-container {
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: top center;
}

.main-pic {
  width: 100%;
}

.icard {
  background: white;
  padding: 30rpx 40rpx 50rpx 58rpx;

  &.options {
    padding: 30rpx 40rpx 50rpx 28rpx;
    margin-top: 20rpx;
  }

  .price-bar {
    @include center();
    justify-content: flex-start;

    .price {
      display: flex;
      align-items: flex-end;
      font-weight: bold;
      font-size: 34rpx;
      color: #FF4D4F;

      .mini {
        font-size: 20rpx;
      }
    }

    .days {
      margin-left: 10rpx;
      background: #E7F4FF;
      border-radius: 24rpx;
      font-weight: bold;
      font-size: 20rpx;
      color: #1890FF;
      padding: 10rpx 24rpx;
    }
  }

  .name {
    margin-top: 30rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: $black-color;
  }

  .prop {
    margin-top: 20rpx;
    display: flex;
    align-items: flex-start;

    .prop-name {
      font-size: 28rpx;
      color: $black-color-v3;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .prop-value {
      margin-left: 8rpx;
      font-size: 28rpx;
      color: $black-color;
      flex-grow: 1;
      word-wrap: break-word;
    }
  }

  .title {
    font-weight: bold;
    font-size: 28rpx;
    color: $black-color;
    margin-bottom: 20rpx;
  }

  .skus {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;
    padding-left: 30rpx;

    .sku {
      padding: 16rpx 20rpx;
      background: #EDEEF0;
      border-radius: 20rpx;
      border: 2rpx solid transparent;
      flex-shrink: 0;
      white-space: nowrap;

      &.active {
        border-color: #1890FF;
      }
    }
  }

  .dates {
    @include center();
    justify-content: flex-start;
    padding-left: 30rpx;

    .input {
      padding: 16rpx 40rpx;
      background: #EDEEF0;
      border-radius: 20rpx;
      @include center();
      justify-content: flex-start;
      font-size: 28rpx;
      color: $black-color;
    }
  }
}

.tabs {
  padding: 30rpx 26rpx 0 28rpx;
  @include center();
  justify-content: flex-start;

  .tab {
    @include center(column);
    font-weight: bold;
    font-size: 34rpx;
    color: $black-color;
    margin-left: 45rpx;
    margin-right: 44rpx;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    .bar {
      margin-top: 20rpx;
      width: 68rpx;
      height: 8rpx;
      background: #1890FF;
      visibility: hidden;

      &.active {
        visibility: visible;
      }
    }
  }
}

.trip {
  @include center(column);

  .top {
    width: 100%;
    @include center();
    justify-content: space-between;
    background: white;
    padding: 30rpx 58rpx 30rpx 28rpx;

    & > text {
      font-weight: bold;
      font-size: 34rpx;
      color: $black-color;
    }

    :deep(.my-button) {
      font-size: 20rpx;
      padding: 10rpx 32rpx;
    }
  }

  .schedule {
    margin-top: 20rpx;
    background: white;

    .day {
      margin-top: 30rpx;
      margin-left: 28rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: $black-color;
    }

    .short-desc {
      margin: 20rpx 52rpx 0 58rpx;
      font-size: 28rpx;
      color: $black-color;
    }

    .title {
      margin: 20rpx auto;
      width: 694rpx;
      padding: 20rpx 0 20rpx 30rpx;
      background: #E7F4FF;
      font-weight: bold;
      font-size: 28rpx;
      color: $black-color;
    }

    .content {
      width: 636rpx;
      margin: 0 auto;
      padding-bottom: 20rpx;
    }
  }
}

.desc {
  background: white;
  padding: 20rpx;
}

.cost {
  background: $page-bg-color;
  padding: 20rpx;
}

.refundRule {
  background: white;
  @include center(column);
  align-items: flex-start;
  gap: 10rpx;
  padding: 20rpx;
}

.footer {
  position: fixed;
  bottom: 0%;
  width: 100%;
  display: flex;
  background: white;
  height: 144rpx;
  @include center();
  justify-content: space-between;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  border-radius: 20rpx 20rpx 0rpx 0rpx;

  .left {
    margin-left: 28rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: #FF4D4F;
  }

  .right {
    margin-right: 28rpx;
  }
}