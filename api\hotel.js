import request from '../utils/request.js'

export function hotelSearch(data, loading = true) {
  return request({
    method: 'get',
    url: '/api/v1/front/hotel/search',
    data,
  }, loading)
}

export function hotelRoomPolicy(data, loading = true) {
  return request({
    url: '/api/v1/front/hotel/roompolicy',
    method: 'get',
    data,
  }, loading)
}

export function hotelDetail(id) {
  return request({
    url: '/api/v1/front/hotel/detail',
    method: 'get',
    data: {
      id
    }
  })
}