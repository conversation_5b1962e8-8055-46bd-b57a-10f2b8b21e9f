<script setup>

const props = defineProps({
  name: {
    type: String,
    default: () => '',
  }
})

</script>

<template>
  <view>
    <view class="header">
      <view class="map">
        <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/wifi/header_wifi.png"></image>
      </view>
    </view>
    <view class="brand-name">{{ name }}</view>
  </view>

</template>

<style lang="scss" scoped>
@use "../../../styles/mix";

.header,
.map {
  background-size: contain;
}

.header {
  background-image: url('https://rp.yjsoft.com.cn/yiban/static/wifi/header_bg.png');
  width: 100%;
  height: 376rpx;
  @include mix.center();
  position: relative;

  .map {
    background-image: url('https://rp.yjsoft.com.cn/yiban/static/wifi/header_map.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    width: 515rpx;
    height: 286rpx;
    @include mix.center();
    position: absolute;
    bottom: 18rpx;

    image {
      width: 224rpx;
    }
  }
}

.brand-name {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  padding: 20rpx 0;
}
</style>