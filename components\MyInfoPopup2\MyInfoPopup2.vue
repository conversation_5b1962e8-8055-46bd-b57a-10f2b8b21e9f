<template>
  <uni-popup ref="popup" :mask-click="maskClick" type="bottom"
             @change="handleChange">
    <!-- 额外的遮罩层事件拦截，仅阻止滑动穿透，不阻止点击事件 -->
    <view v-if="isOpen" class="mask-event-blocker" @touchmove.prevent @wheel.prevent></view>

    <view class="my-info-popup2" @touchmove.prevent @wheel.prevent>
      <view class="close">
        <view>
          <text class="iconfont icon-guanbi" @tap="onClose"></text>
        </view>

      </view>
      <view class="container">
        <view v-if="title.length > 0" class="title">
          {{ title }}
        </view>
        <slot></slot>
        <view v-if="$slots.footer" class="footer">
          <slot name="footer"></slot>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import {computed, onMounted, ref, onBeforeUnmount, nextTick} from 'vue';

const popup = ref(null)
const emit = defineEmits(['close', 'action'])
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  maskClick: {
    type: Boolean,
    default: true
  }
})
const content = computed(() => props.text)
const isOpen = ref(false)

// 处理滚动穿透
function handleScroll(e) {
  // 只处理滚动事件，不阻止点击
  if (e.type === 'touchmove' || e.type === 'wheel') {
    e.preventDefault()
    e.stopPropagation()
  }
  return false
}

// 禁止页面滚动
function disableScroll() {
  // 对所有平台使用通用方案
  // #ifdef H5
  try {
    const scrollY = window.scrollY || 0
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.top = `-${scrollY}px`
    document.body.style.height = '100%'

    // 添加全局事件监听，不直接修改第三方组件
    document.addEventListener('touchmove', handleScroll, {
      passive: false
    })

    // 使用CSS选择器添加事件，不修改组件源码
    nextTick(() => {
      try {
        // 使用事件代理，为所有.uni-transition元素添加事件监听
        document.querySelectorAll('.uni-popup .uni-transition').forEach(el => {
          el.addEventListener('touchmove', handleScroll, {passive: false})
          el.addEventListener('touchstart', handleScroll, {passive: false})
          el.addEventListener('wheel', handleScroll, {passive: false})
          el.style.touchAction = 'none'
        })
      } catch (e) {
        console.error('Failed to add event listeners:', e)
      }
    })
  } catch (e) {
    console.error('Failed to disable scroll:', e)
  }
  // #endif

  // #ifdef APP-PLUS
  // 仅使用uni-app API，不修改组件
  try {
    uni.setPageMeta({
      pageStyle: {
        overflow: 'hidden'
      }
    })
  } catch (e) {
    console.error('Failed to set page style:', e)
  }
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序在模板中已通过page-meta处理
  // #endif
}

// 恢复页面滚动
function enableScroll() {
  // #ifdef H5
  try {
    const scrollYString = document.body.style.top || '0'
    const scrollY = parseInt(scrollYString.replace('px', ''))
    document.body.style.overflow = ''
    document.body.style.position = ''
    document.body.style.width = ''
    document.body.style.top = ''
    document.body.style.height = ''
    window.scrollTo(0, Math.abs(scrollY))

    // 移除全局事件监听
    document.removeEventListener('touchmove', handleScroll)

    // 移除所有添加的事件处理
    try {
      document.querySelectorAll('.uni-popup .uni-transition').forEach(el => {
        el.removeEventListener('touchmove', handleScroll)
        el.removeEventListener('touchstart', handleScroll)
        el.removeEventListener('wheel', handleScroll)
      })
    } catch (e) {
      console.error('Failed to remove event listeners:', e)
    }
  } catch (e) {
    console.error('Failed to enable scroll:', e)
  }
  // #endif

  // #ifdef APP-PLUS
  try {
    uni.setPageMeta({
      pageStyle: {
        overflow: 'visible'
      }
    })
  } catch (e) {
    console.error('Failed to reset page style:', e)
  }
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序在模板中已通过page-meta处理
  // #endif
}

function handleChange(e) {
  isOpen.value = e.show
  if (!e.show) {
    emit('close')
    // 弹窗关闭时恢复滚动
    enableScroll()
  } else {
    // 弹窗打开时禁止滚动
    disableScroll()
  }
}

function onClose() {
  popup.value.close()
}

onMounted(() => {
  popup.value.open()
})

// 组件销毁时确保恢复滚动
onBeforeUnmount(() => {
  if (isOpen.value) {
    enableScroll()
  }
})
</script>

<style lang="scss">
@use '../../styles/_define.scss';
@use '../../styles/_mix.scss';

/* 全局覆盖uni-popup样式，防止滑动穿透 */
:deep(.uni-popup) {
  position: relative;
  z-index: 999;
}

:deep(.uni-popup .uni-transition[name="mask"]) {
  touch-action: none !important;
  pointer-events: auto !important; /* 确保可以接收事件 */
}

:deep(.uni-popup .uni-transition) {
  position: fixed;
}

:deep(.uni-popup__wrapper) {
  touch-action: none !important;
  z-index: 1000 !important; /* 确保内容区域在遮罩层之上 */
}

/* 额外的遮罩层事件拦截，不修改第三方组件 */
.mask-event-blocker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 900; /* 确保在内容区域下方 */
  pointer-events: none; /* 默认不拦截点击事件 */
}

/* 仅在弹出层显示时阻止滑动事件 */
.mask-event-blocker:active {
  pointer-events: auto; /* 仅在触摸时拦截事件 */
  touch-action: none;
}

.my-info-popup2 {
  position: relative;
  z-index: 1000; /* 确保在遮罩层之上 */
  .container {
    background: #F7F7F9;
  }

  .container, .title, .footer {
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
  }

  .close {
    padding-bottom: 20rpx;

    &, view {
      @include mix.center();
    }

    view {
      width: 42rpx;
      height: 42rpx;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
    }

    text {
      color: white;
      font-size: 24rpx;
    }
  }

  .title, .footer {
    background: #FFFFFF;
  }

  .title {
    @include mix.center();
    font-weight: 600;
    font-size: 34rpx;
    padding: 30rpx 0;
  }

  .footer {
    padding: 36rpx 28rpx;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  }
}
</style>
