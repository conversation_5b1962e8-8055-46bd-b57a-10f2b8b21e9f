<script setup>
import { useGlobalStore } from '@/store/global'
import { computed, onMounted, ref, watch } from 'vue';
import { navTo } from '@/utils';
import SearchBar from './searchBar.vue';
import { sceneSearch } from "@/api/scene";

const props = defineProps({
  active: <PERSON><PERSON><PERSON>,
  query: null,
  height: 0,
})
let page = 1
const globalStore = useGlobalStore()
const city = computed(() => globalStore.location)

const params = ref({
  keyword: '',
  order: 'desc',
})
const list = ref([])
const hasMore = ref(true)

const doLoad = async (reset = false) => {
  if (reset) {
    page = 1
    list.value = []
  }
  const { data } = await sceneSearch({
    ...params.value,
    page,
  })

  list.value.push(...data.list)
  hasMore.value = data.list.length > 0
}

const doLoadNext = () => {
  if (!hasMore.value) {
    return
  }
  page++
  doLoad()
}

watch(city.value, () => {
  if (city.value.zone_id) {
    Object.assign(params.value, {
      zone_ids: city.value.zone_id,
    })
    doLoad(true)
  }
})

onMounted(async () => {
  Object.assign(params.value, props.query)
  if (!params.value.zone_ids) {
    let loc = await globalStore.getLocation()
    params.value.zone_ids = loc.zone_id
  }
  doLoad()
})

</script>

<template>
  <view class="body">
    <SearchBar v-model="params.keyword" :city="city" placeholder="景点" @confirm="doLoad(true)"></SearchBar>
    <scroll-view class="content" scroll-y @scrolltolower="doLoadNext" enhanced :show-scrollbar="false">
      <view class="main">
        <view v-for="item in list" :key="item.id" class="list-item"
          @tap="navTo('pages/scenic/scenic', { id: item.id })">
          <view class="item-info">
            <view class="pic">
              <image :src="item.pic" mode="aspectFill" />
              <view v-if="item.level" class="score">{{ `${item.level}A` }}</view>
            </view>
            <view class="right">
              <view class="name">
                <view>{{ item.name }}</view>
              </view>
              <view class="open-time">
                <text class="iconfont icon-shijian" />
                营业中 {{ item.open_time }}
              </view>
              <view>
                <view class="address ltxt">
                  <text class="iconfont icon-ditu" />
                  {{ item.address }}
                </view>
                <view v-if="item.cost_text" class="cost-text">
                  均价：
                  <text>{{ item.cost_text }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-if="item.tags.length > 0" class="tag-list">
            <text v-for="tag in item.tags" :key="tag">{{ tag }}</text>
          </view>

        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

.body {
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: hidden;

  .sorter {
    display: flex;
    gap: 30rpx;
    margin: 20rpx 0 20rpx 28rpx;

    .sitem {
      @include center();
      gap: 20rpx;
      color: #333333;
      font-size: 24rpx;

      & .iconfont {
        font-size: 20rpx;
      }

      &.active {
        color: #1890FF;
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;

    .main {
      display: flex;
      flex-direction: column;

      .list-item {
        margin: 0 $padding-page 30rpx;
        border-radius: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .tag-list {
          margin-top: 20rpx;
        }

        .item-info {
          @include center();
          justify-content: flex-start;
          align-items: stretch;
          gap: 16rpx;

          .pic {
            width: 160rpx;
            height: 160rpx;
            border-radius: $border-radius-v2;
            position: relative;

            .score {
              position: absolute;
              bottom: 0;
              left: 0;
              z-index: 10;
              height: 48rpx;
              width: 48rpx;
              @include center();
              border-radius: 50%;
              font-size: 24rpx;
              background: linear-gradient(180deg, #FDE72D 0%, #FACA14 100%);
            }

            image {
              border-radius: $border-radius-v2;
            }
          }

          .right {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 6rpx;
            font-size: 24rpx;

            .name {
              @include center();
              justify-content: flex-start;
              font-weight: bold;
              gap: 10rpx;

              >view {
                &:first-child {
                  max-width: 340rpx;
                  font-size: 28rpx;
                  @include ellipse();
                }
              }
            }

            .open-time {
              @include ellipse();
              max-width: 500rpx;
            }

            view {
              &:last-child {
                @include center();
                justify-content: space-between;

                .address {
                  max-width: 360rpx;
                  @include ellipse();
                }
              }

              .cost-text {
                font-size: 20rpx;
                color: #1890FF;

                text {
                  font-weight: bold;
                  font-size: 28rpx;
                }
              }
            }
          }


        }
      }
    }
  }
}
</style>
