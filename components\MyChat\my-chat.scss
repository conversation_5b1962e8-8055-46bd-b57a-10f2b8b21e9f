@import '../../styles/mix';
@import '../../styles/define';

.top-logo {
  @include center(column);
  margin-bottom: 60rpx;
  padding: 14rpx 14rpx 0;

  image {
    width: 251rpx;
    height: 169rpx;
    margin-bottom: 10rpx;
  }

  .slogan {
    font-size: 20rpx;
    color: #999999;
  }

  .prompts {
    margin-top: 60rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    > view {
      background: #FFFFFF;
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
      border-radius: 8rpx;
      padding: 8rpx 20rpx;
      font-size: 24rpx;
    }
  }
}

/* 添加思考中文本动画样式 */
.thinking-dots-container {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 靠左对齐 */
  padding: 20rpx 30rpx;
  margin: 20rpx 28rpx; /* 左右与其他消息保持一致的边距 */
  background-color: white;
  border-radius: 0rpx 20rpx 20rpx 20rpx; /* 恢复左上角没有圆角的样式 */
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
  width: fit-content; /* 使宽度刚好适应内容 */
  max-width: 60%; /* 控制最大宽度 */
}

.thinking-text-animation {
  color: #666;
  font-size: 28rpx;
}

.dot-animation {
  font-size: 40rpx;
  line-height: 1;
  color: #666;
  margin-left: 2rpx;
}

.dot1 {
  animation: dot-jump 1.2s infinite 0s;
}

.dot2 {
  animation: dot-jump 1.2s infinite 0.2s;
}

.dot3 {
  animation: dot-jump 1.2s infinite 0.4s;
}

@keyframes dot-jump {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
  100% {
    transform: translateY(0);
  }
}

.my-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // 覆盖index.scss中的背景色
  background: linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1)) !important;
  // 确保背景覆盖整个容器
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.title_icon {
  margin-left: 20rpx;
  width: 48rpx;
  height: 48rpx;
}

scroll-view {
  flex: 1;
  overflow-y: auto;
  // 添加硬件加速，提高滚动性能
  will-change: transform;
  // 添加平滑滚动
  scroll-behavior: smooth;

  .chat-content {
    padding-top: 20rpx;
    margin-left: 28rpx;
    margin-right: 28rpx;
    display: flex;
    margin-bottom: 30rpx;
    // 使用transform而不是margin，提高渲染性能
    transform: translateZ(0);

    &.user {
      justify-content: flex-end;

      .message-container {
        align-items: flex-end;

        .content {
          padding: 18rpx 30rpx 20rpx 30rpx;
          background: #1890FF;
          border-radius: 20rpx 0rpx 20rpx 20rpx;
          color: white;
          word-break: break-all;
        }
      }
    }

    &.system {
      justify-content: flex-start;

      .message-container {
        align-items: flex-start;
      }

      .content, .suggestions text {
        background: white;
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
      }

      .content {
        padding: 30rpx;
        width: 100%;

        :deep(.rich-text-content) {
          width: 100%;
        }

        .thinking {
          margin-bottom: 10rpx;

          .title {
            color: #666666;
            @include center();
            justify-content: flex-start;

            .iconfont {
              background: #D8D8D8;
              border-radius: 50%;
              width: 32rpx;
              height: 32rpx;
              @include center();
            }

            margin-bottom: 10rpx;

            &.close {
              .iconfont {
                transform: rotate(180deg);
              }
            }
          }

          .thinking-text {
            border-left: 2rpx solid #979797;
            padding-left: 16rpx;
          }
        }
      }

      .suggestions {
        display: flex;
        flex-direction: column;
        gap: 10rpx;
        font-size: 20rpx;
        margin-top: 10rpx;

        text {
          padding: 10rpx 30rpx;
        }
      }
    }

    .message-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .plan-item {
        width: 100%;
      }


    }
  }
}

/* 建议菜单样式 */
.suggestions-container {
  display: flex;
  justify-content: flex-start; /* 改为左对齐，确保可滚动 */
  margin-bottom: -5rpx; /* 减小与输入框的距离 */
  padding: 0 20rpx; /* 保持左右内边距 */
  overflow-x: auto; /* 启用水平滚动 */
  width: 100%; /* 确保容器占满宽度 */
  scrollbar-width: none; /* 隐藏滚动条 */
  -ms-overflow-style: none; /* IE 和 Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.suggestions-wrapper {
  display: flex;
  flex-wrap: nowrap; /* 保持一行显示 */
  justify-content: flex-start; /* 改为左对齐，确保左侧内容可访问 */
  width: auto; /* 改为auto，让内容自然流动 */
  min-width: 100%; /* 当内容不足时仍能占满容器宽度 */
  align-items: center; /* 垂直居中对齐 */
  gap: 16rpx; /* 使用gap替代margin，更好的间距控制 */
  padding: 0; /* 移除内边距 */
}

.suggestion-item {
  background-color: #CBFFFE; /* 浅青色背景 */
  color: #333;
  border: 2rpx solid #93EBED; /* 青色边框 */
  border-radius: 20rpx;
  padding: 10rpx 20rpx; /* 稍微减小内边距 */
  margin: 0; /* 移除margin，使用父容器的gap */
  font-size: 24rpx; /* 减小字体大小 */
  text-align: center;
  flex-shrink: 0; /* 防止项目被压缩 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;

  /* 淡入动画效果 */
  &.suggestion-fade-in {
    animation: suggestionFadeIn 0.3s ease-in-out;
  }
}


/* 建议项目淡入动画 */
@keyframes suggestionFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.footer {
  padding: $padding-page $padding-page 34rpx $padding-page;

  .searchbox {
    gap: 20rpx;
    @include center();
    justify-content: center;
    margin-bottom: 20rpx;

    :deep(.my-button) {
      border: 2rpx solid #EDEEF0;
      color: #666666;
      border-radius: 20rpx;
    }
  }
}
