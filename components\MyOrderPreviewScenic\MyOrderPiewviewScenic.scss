@import '@/styles/_define.scss';
@import '@/styles/_mix.scss';
@import '@/styles/tour.scss';

.container {
	background: $page-bg-color;
	min-height: 100vh;
	padding-bottom: 150rpx;
}

.banners {
	height: 420rpx;

	swiper {
		width: 100%;
		height: 100%;

		swiper-item {
			width: 100%;
			height: 100%;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}
}

.scenic-cont {
	margin: 0rpx auto;
	background: #ffffff;
	border-radius: $border-radius-middle $border-radius-middle 0rpx 0rpx;
	position: relative;
	top: -50rpx;
	padding: $padding-page;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	font-size: $fontsize-small;

	.title {
		font-size: $h3-v2;
		font-weight: 500;
		@include ellipse();
	}
	.price {
		text-align: right;
		color: $plan-color-1;
		font-size: $h3-v2;
		margin-top: 31rpx;
	}
}

.card {
	margin: 0 $padding-page $padding-small $padding-page;
	background: transparent;
	border-radius: 0;
	padding: 0;

	.title {
		font-weight: 500;
		margin-bottom: $padding-page;
	}
}

.days {
	.content {
		display: flex;
		justify-content: space-between;

		.price,
		.more {
			color: $plan-color-1;
		}

		.left {
			flex: 1;
			display: flex;
			gap: 17rpx;
			padding-right: 17rpx;

			.item {
				font-size: 24rpx;
				@include center(column);
				gap: $padding-small;
				width: 122rpx;
				height: 174rpx;
				background: white;
				border: 1rpx solid $border-color-v2;

				&.active {
					background: #fff4f1;
					border-color: #ff957f;
				}
			}
		}

		.more {
			@include center();

			> view {
				width: 55rpx;
			}
		}
	}
}

.popup {
	padding-bottom: 100rpx;

	.people-tips {
		font-size: $fontsize-small;
		color: #b7b9bd;
	}
}

.peoples {
	.title {
		display: flex;

		view {
			color: $font-color-gray;
			margin-left: $padding-v2;
		}
	}
	.shorts {
		@include center();
		justify-content: flex-start;
		margin: $padding-page 0;
		gap: $padding-v2;
		scroll-view {
			white-space: nowrap;
			width: 72%;

			.people-items {
				width: 100%;
				display: flex;
				gap: $padding-small;
			}
		}

		.people-items view,
		.plus {
			border: 1rpx solid $border-color-v2;
			background: white;
			padding: $padding-small $padding-middle;
			border-radius: $border-radius-v2;
			font-size: $fontsize-small;
			@include center();

			&.active {
				background: #fff4f1;
				border-color: $plan-color-1;
			}
		}

		&.plus {
			flex: 1;
			width: 156rpx;
			color: $plan-color-1;
		}
	}

	.list {
		.item {
			margin-bottom: $padding;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				@include center();
				gap: $padding-v2;

				.delete {
				}
			}

			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}

.footer {
	position: fixed;
	bottom: 0%;
	width: 100%;
	@include center();
	justify-content: space-between;
	background: white;
	padding: 27rpx $padding-page;

	.apply {
		padding: 15rpx 41rpx;
	}
	.price {
		color: $plan-color-1;
		font-size: $h3-v2;
		font-weight: 600;
	}
}
