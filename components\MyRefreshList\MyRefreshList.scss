.my-refresh-list-wrapper {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.my-refresh-list {
  width: 100%;
  box-sizing: border-box;
}

.custom-refresh-area {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  transition: all 0.3s ease;
  overflow: hidden;

  .default-refresh-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;

    .refresh-text {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.list-content {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.list-items {
  flex: 1;
  width: 100%;
}

.list-item {
  width: 100%;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200rpx;
  
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .empty-text {
      font-size: 28rpx;
      color: #999999;
      text-align: center;
    }
  }
}

.load-more-container {
  width: 100%;
  padding: 20rpx 0;
}
