<template>
	<view class="container">
		<!-- 轮播图 -->
		<view class="hotel-banner">
			<swiper autoplay="true">
				<swiper-item v-for="(item, index) in [detail.pic]" :key="index">
					<image :src="item"></image>
				</swiper-item>
			</swiper>
		</view>
		<!-- 酒店内容 -->
		<view class="hotel-cont">
			<view class="title">
				<view class="left">
					<text class="cont-title">
						{{ detail.name }}
					</text>
					<view>
						<text class="iconfont icon-vip-diamond-fill star-text" :key="item"
							v-for="item in new Array(detail.star)"></text>
					</view>
				</view>

				<text class="score" v-if="detail.score">{{ detail.score }}</text>
			</view>

			<view class="tags">
				<view v-if="detail.open_up_time" class="tag primary-v2">{{ detail.open_up_time }}年开业</view>
				<view class="tag primary-v2" v-for="item in tags" :key="item">{{ item }}</view>
			</view>

			<view class="line-2">
				<view class="good-rate" v-if="detail.good_rate">
					<text class="iconfont icon-geigehaoping"></text>好评率：
					<text class="value">{{ `${detail.good_rate}%` }}</text>
				</view>

				<view class="cost">
					<text class="iconfont icon-icon"></text>均价：
					<text class="value">{{ detail.cost_text }}</text>
				</view>
			</view>
			<view>{{ detail.address }}</view>

			<view class="tel-locaton">
				<view @tap="onCall"><text class="iconfont icon-dianhua1 tel"></text>电话</view>
				<view @tap="openLocation">
					<text class="iconfont icon-ditu location"></text>地图
				</view>
			</view>

			<YjTabs active-color="#1890FF" v-model="activeTab" :data="tabData"></YjTabs>

			<view class="options">
				<MyHotelCheckin @tap="showDayPopup = true" class="checkin" :checkin="checkin" :checkout="checkout">
				</MyHotelCheckin>
				<view class="peoples" @tap="showPeoples = true">
					<view class="h">房间人数</view>
					<view>
						<view>{{ roomNum }}<text class="iconfont icon-chuang"></text></view>
						<view>{{ adult }}<text class="iconfont icon-chengren"></text></view>
						<view>{{ children }}<text class="iconfont icon-ertongpiao"></text></view>
					</view>
				</view>
			</view>
		</view>

		<view id="rooms" class="rooms">
			<view class="room-item no-more" v-if="rooms.length == 0">
				<view>
					<text class="iconfont icon-huojiakongkong"></text>
					暂无可预定房型
				</view>
			</view>
			<view class="room-item" v-for="room in showRooms" :key="room.id">
				<view @tap="handleRoom(room)" class="room" :class="{
					open: showPolicy[room.id]
				}">
					<view class="left">
						<image :src="room.pic" mode="scaleToFill"></image>
					</view>
					<view class="right">
						<view>
							<view class="room-name">
								{{ room.name }}
								<text class="iconfont icon-xiangxia" :class="{
									'icon-xiangxia': !showPolicy[room.id],
									'icon-xiangshang': showPolicy[room.id]
								}"></text>
							</view>
							<view class="bed_desc">{{ room.bed_desc + ' ' + room.attrs.join(' ') }}</view>
						</view>

						<view class="price-area">
							<view class="warn">
								<view class="tag" v-if="room.stock_warn">
									库存紧张
								</view>
							</view>
							<view class="sale-price">
								{{ formatMoney(room.sales_price) }}
								<text>均</text>
							</view>
						</view>
					</view>
				</view>
				<view class="policy" :class="{ show: showPolicy[room.id] }">
					<view class="policy-item" v-for="policy in room.policy" :key="policy.id">
						<view class="policy-name">
							<view class="breakfast">{{ policy.breakfast }}</view>
							<view class="cancel-rule">{{ policy.cancel_desc }}</view>
						</view>
						<view class="actions">
							<view class="left">
								<view>
									<view class="tag cancel" v-if="policy.confirm_desc">{{ policy.confirm_desc }}</view>
									<view class="tag">可住{{ policy.person }}人</view>
								</view>

								<text class="lock">锁定房源</text>
							</view>
							<view class="right">
								<view class="sale-price">{{ formatMoney(policy.sales_price) }}</view>
								<view @tap="handlePolicy(room, policy)" class="btn danger">订</view>
							</view>
						</view>
					</view>
				</view>

			</view>

			<uni-load-more @clickLoadMore="() => {
				currentShowRoomIndex = rooms.length
			}" :content-text="{
				contentdown: '点击显示更多',
			}" v-if="showRooms.length < rooms.length"></uni-load-more>

		</view>

		<!-- 民宿详情 -->
		<view id="hotel-detail" class="hotel-detail">
			<view class="rich">
				<rich-text :nodes="richtext(desc, false)"></rich-text>
			</view>
		</view>

		<MyInfoPopup type="bottom" class="date-dialog" title="选择日期" @close="handleDateChange" v-if="showDayPopup">
			<my-calendar type="range" @change="onDateChange" :month="datepickerMonth" :value="range"></my-calendar>
		</MyInfoPopup>

		<MyInfoPopup type="bottom" class="peoples-dialog" v-if="showPeoples" @close="onPeoplesChange" title="选择入住人数">
			<view class="notice">
				<text class="iconfont icon-wenxintishi"></text>入住人数较多时，试试增加间数
			</view>
			<view class="list-item">
				<view>房间数</view>
				<uni-number-box min="1" v-model="roomNum"></uni-number-box>
			</view>
			<view class="list-item">
				<view>成人</view>
				<uni-number-box min="1" v-model="adult"></uni-number-box>
			</view>
			<view class="list-item">
				<view>儿童</view>
				<uni-number-box v-model="children"></uni-number-box>
			</view>
		</MyInfoPopup>

	</view>
</template>

<script setup>
import {
	computed,
	ref,
	watch
} from 'vue';
import {
	onLoad,
	onShareAppMessage
} from '@dcloudio/uni-app'
import {
	hotelDetail,
	hotelRoomPolicy
} from '../../api/hotel';

import qs from 'qs'

import {
	HotelGuestTypeMainland,
	HotelGuestTypeMap
} from '../../utils/constmap';
import dayjs from 'dayjs';
import {
	formatTime,
	formatMoney,
	richtext
} from '../../utils/index.js'
import YjTabs from '@/components/YjTabs/YjTabs.vue';
import MyCalendar from "@/components/MyCalendar/MyCalendar.vue";

const tabData = [{
	label: '酒店预定',
	value: 'booking'
},
{
	label: '图片/视频',
	value: 'desc'
},
{
	label: '设施周边',
	value: 'fac'
},
{
	label: '订房必读',
	value: 'notice'
},
]
const activeTab = ref('booking')

const detail = ref({
	pics: [],
	star: 0,
	score: 4.8,
	open_up_time: '',
	tel: '',
	lat: '',
	lng: '',
	guest_type: [],
})
const checkin = ref(dayjs().add(1, 'day').toDate())
const checkout = ref(dayjs().add(2, 'day').toDate())
const adult = ref(1)
const children = ref(0)
const range = ref([dayjs(checkin.value).toDate(), dayjs(checkout.value).toDate()])
const datepickerMonth = computed(() => dayjs(range.value[0]).format('YYYY-MM'))
const roomNum = ref(1)
const showDayPopup = ref(false)
const showPolicy = ref({})
const showPeoples = ref(false)

watch(range, newVal => {
	checkin.value = newVal[0]
	checkout.value = newVal[1]

	getRooms()
})

const tags = computed(() => {
	const list = []

	if (detail.value.brand_name) {
		list.push(detail.value.brand_name)
	}
	if (detail.value.guest_type.length > 0) {
		list.push(...detail.value.guest_type.map(item => {
			const t = HotelGuestTypeMap[item]

			return t ?? HotelGuestTypeMap[HotelGuestTypeMainland]
		}))
	}

	return list
})

const desc = computed(() => {
	const content = detail.value.pics.reduce((str, item) => {
		str += `<img src="${item}"/>`
		return str
	}, '')

	return content
})
const id = ref(0)
const rooms = ref([])
const maxShowRooms = 3
const currentShowRoomIndex = ref(maxShowRooms)
const showRooms = computed(() => {
	const list = []
	for (let i = 0; i < rooms.value.length; i++) {
		if (i >= currentShowRoomIndex.value) {
			break
		}

		list.push(rooms.value[i])
	}

	return list
})

function onDateChange(value) {
	range.value = value
	showDayPopup.value = false
}

function onPeoplesChange() {
	showPeoples.value = false
}

watch(activeTab, val => {
	const query = uni.createSelectorQuery()

	if (val == 'booking') {
		query.select('#rooms').boundingClientRect()
	} else {
		query.select('#hotel-detail').boundingClientRect()
	}

	query.exec(rect => {
		if (rect[0]) {
			uni.pageScrollTo({
				scrollTop: rect[0].top,
				duration: 300,
			})
		}
	})

})

function openLocation() {
	uni.openLocation({
		latitude: detail.value.lat,
		longitude: detail.value.lng,
		name: detail.value.name
	})
}

function handlePolicy(room, policy) {
	const params = {
		hotel_id: detail.value.id,
		room_id: room.id,
		policy_id: policy.id,
		checkin: dayjs(checkin.value).unix(),
		checkout: dayjs(checkout.value).unix(),
		ota_code: room.ota_code,
		product_type: room.product_type,
		ota_id: room.ota_id,
		type: 'hotel',
		num: roomNum.value,
	}

	uni.navigateTo({
		url: '/pages/orderpreview/orderpreview?' + qs.stringify(params)
	})
}

function handleDateChange() {
	showDayPopup.value = false
}

function handleRoom(room) {
	if (showPolicy.value[room.id]) {
		delete showPolicy.value[room.id]
		return
	}
	showPolicy.value[room.id] = true
}

function getRooms() {
	const params = {
		hotel_id: id.value,
		start: dayjs(checkin.value).unix(),
		end: dayjs(checkout.value).unix()
	}
	hotelRoomPolicy(params).then(res => {
		const {
			data
		} = res
		rooms.value = []

		data.otas.forEach(ota => {
			rooms.value.push(...ota.rooms.map(item => {
				item.ota_name = ota.ota_code_name
				item.sales_price = 0
				item.market_price = 0
				item.ota_code = ota.ota_code
				item.product_type = ota.product_type
				item.ota_id = ota.ota_id

				item.policy.forEach(policy => {
					item.sales_price += policy.sales_price
					if (policy.sales_price > item.market_price) {
						item.market_price = policy.sales_price
					}
				})
				item.sales_price /= item.policy.length

				item.stock_warn = item.policy.findIndex(p => p.remain_rooms <= 3) > -1

				return item
			}))
		})
	})
}

function onCall() {
	if (detail.value.tel.length == 0) {
		return
	}

	const tmp = detail.value.tel.split(';')
	uni.makePhoneCall({
		phoneNumber: tmp[0]
	})
}

onShareAppMessage(() => {
	return {
		title: detail.value.name,
		imageUrl: detail.value.pic
	}
})

onLoad((query) => {
	id.value = query.id

	hotelDetail(id.value).then(res => {
		const {
			data
		} = res
		Object.assign(detail.value, data)

		uni.setNavigationBarTitle({
			title: detail.value.name,
		})

		if (activeTab.value == 'booking') {
			getRooms()
		}
	})
})
</script>

<style lang="scss" scoped>
@import 'hotel.scss'
</style>