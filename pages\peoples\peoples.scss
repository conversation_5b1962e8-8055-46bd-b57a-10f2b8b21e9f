@import '../../styles/_mix.scss';
@import '../../styles/_define.scss';

.container {
  min-height: 100vh;
  background: $page-bg-color;
}

:deep(.tabs) {
  padding: 0 $padding-page;
}

.buttons {
  background: #F7F7F9;
  margin-top: $padding-page;

  .d {
    @include center();
    padding: $padding-page;
    background: white;
    box-shadow: 0rpx 0rpx 7rpx 0rpx rgba(0, 0, 0, 0.1);

    > view {
      border-right: 1rpx solid $border-color-v2;
      flex: 1;
      @include center(column);
      padding: $padding-middle 0;
      gap: $padding-small;
      border-radius: $border-radius-middle;

      &:last-child {
        border-right: none;
      }

      .iconfont {
        color: $plan-color-3;
      }
    }
  }
}

.list {
  display: flex;
  flex-direction: column;
  margin-top: $padding-page;
  gap: $padding-page;
  padding: $padding-page;

  .list-item {
    .name {
      font-weight: 600;
    }

    .info, .info .left {
      @include center();
    }

    .info {
      justify-content: space-between;

      .left {
        radio {
          transform: scale(0.8);
        }
      }

      .label {
        margin-right: $padding-page;
      }
    }

  }
}