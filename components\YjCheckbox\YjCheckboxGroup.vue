<script setup>

import {onMounted, provide} from "vue";

const emit = defineEmits(['change'])

const value = defineModel({
  type: Array,
  default: () => []
})

function addValue(val) {
  if (!value.value.includes(val)) {
    value.value.push(val)
    emit('change', value.value)
  }
}

function removeValue(val) {
  if (value.value.includes(val)) {
    value.value.splice(value.value.indexOf(val), 1)
    emit('change', value.value)
  }
}

provide('yj-checkbox-group', {addValue, removeValue})

onMounted(() => {
  if (!(value.value instanceof Array)) {
    value.value = []
  }
})

</script>

<template>
  <view class="yj-checkbox-group">
    <slot/>
  </view>
</template>

<style lang="scss" scoped>

</style>