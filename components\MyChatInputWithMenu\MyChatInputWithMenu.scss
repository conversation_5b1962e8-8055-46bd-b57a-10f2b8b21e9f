@import '../../styles/mix';
@import '../../styles/define';

.chat-input-container {
  width: 694rpx; /* 设置固定宽度，750rpx减去两边边距 */
  position: relative;
  z-index: 10;
  background: transparent; /* 整体容器背景透明 */
}

.chat-input-with-menu {
  padding: 0 20rpx;
  height: 90rpx;
  @include center();
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
  background: white; /* 输入框背景为白色 */
  border-radius: 46rpx;
  /* 使用一个box-shadow实现阴影*/
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
}

.voice-ing {
  background: #1890FF;
  color: white;
}

.voice-icon {
  width: 56rpx;
  height: 56rpx;
  flex-shrink: 0;
  font-size: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice {
  flex: 1;
  @include center();
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.iconfont {
  width: 56rpx;
  height: 56rpx;
  color: #008CF7;
  font-size: 50rpx;
  @include center();
}

.iconfont_menu_pic {
  color: #008CF7;
  font-size: 56rpx;
  @include center();
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.menu-trigger {
  width: 56rpx;
  height: 56rpx;
  @include center();
  transition: transform 0.3s ease;

  &.menu-active {
    transform: rotate(45deg);
  }
}

.iconfont_menu {
  font-size: 50rpx;
}

.send {
  width: 56rpx;
  height: 56rpx;
  font-size: 40rpx;
  color: white;
  background: #EDEEF0;
  border-radius: 50%;
  @include center();

  &.active {
    background: #1890FF;
  }
}

.inner {
  justify-content: stretch;
  flex: 1;
  @include center();
  width: 100%;

  input {
    flex: 1;
    height: 100%;
    padding-left: 8rpx;
  }
}

.menu-area {
  width: 100%;
  box-sizing: border-box;
  margin-top: 20rpx;
  animation: fadeIn 0.3s ease-out;
  z-index: 20; /* 确保菜单显示在其他元素之上 */

  .menu-buttons {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    width: 100%;
    box-sizing: border-box;
  }

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: 120rpx;
    margin: 0 10rpx;

    .menu-icon {
      width: 90rpx;
      height: 90rpx;
      background-color: #FFFFFF;
      border-radius: 20rpx;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    }

    .menu-text {
      font-size: 24rpx;
      color: #666666;
      margin-top: 12rpx;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
