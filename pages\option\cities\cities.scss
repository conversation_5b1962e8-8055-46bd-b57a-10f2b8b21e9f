@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

@mixin active {
  background-color: #EDEEF0;
}

.container {
  background: $page-bg-color;
  // min-height: 100vh;
  position: relative;
  padding: $padding-page;
}

.header {
  @include center();
  background: #EDEEF0;
  justify-content: flex-start;
  border-radius: 32rpx;
  padding: 12rpx 24rpx;
  gap: 4rpx;
}

.box {
  margin-bottom: 20rpx;

  .title {
    font-weight: 400;
    padding-bottom: 12rpx;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    > view {
      @include center();
      padding: 10rpx 12rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      font-weight: 500;
      gap: 12rpx;
    }
  }
}

.location-box {
  .iconfont {
    color: #1890FF;
  }
}

.cur-select {
  //margin-top: 21px;
  
  .title {
    color: #1890FF;
  }

  .content {
    view {
      background: #E7F4FF;
      color: #1890FF;
    }
  }
}

.hots {
  border-top: 2rpx solid rgba(237, 237, 237, 0.9);
  margin-bottom: 0;

  .title {
    padding-top: 16rpx;
  }

  .content {
    gap: 16rpx;

    > view {
      padding: 13rpx 48rpx;
    }
  }
}

.filter-list {
  .item {
    @include center();
    justify-content: space-between;
    border-bottom: 2rpx solid rgba(237, 237, 237, 0.9);

    &:active {
      @include active();
    }

    > view {
      @include center();
      padding: 18rpx 0;

      &:first-child {
        .iconfont {
          margin-right: 14rpx;
          color: #B7B9BD;
        }
      }

      &:last-child {
        color: #999999;
      }
    }
  }
}
