<script setup>
import MyCustomerService from "@/components/MyCustomerService/MyCustomerService.vue";
import MyCard from "@/components/MyCard/MyCard.vue";
import { ref } from "vue";
import { formatMoney, formatTime, showToast } from "@/utils";
import { onLoad } from '@dcloudio/uni-app';
import { getRefundDetail } from "@/api/order";
import MyButton from "@/components/MyButton/MyButton.vue";

// 退款详情数据
const refundInfo = ref({})
// 退款订单ID
const refundOrderId = ref('')
// 加载状态
const loading = ref(false)
// 当前显示的退款明细索引，-1 表示没有显示任何退款明细
const currentShowIndex = ref(-1)

// 加载退款详情
function loadRefundDetail() {
  if (!refundOrderId.value) return
  loading.value = true

  getRefundDetail({ refund_order_id: refundOrderId.value })
    .then(res => {
      if (res.code === 0 && res.data) {
        refundInfo.value = res.data
      } else {
        showToast(res.msg || '获取退款详情失败')
      }
    })
    .catch(err => {
      console.error('获取退款详情失败', err)
      showToast('获取退款详情失败')
    })
    .finally(() => {
      loading.value = false
    })
}


// 将页面参数中的refund_order_id转换为数字，并加载退款详情
onLoad((options) => {
  if (options.refund_order_id || options.order_id || options.id) {
    // 优先使用refund_order_id，兼容旧版本使用order_id或id参数的情况
    refundOrderId.value = options.refund_order_id || options.order_id || options.id
    loadRefundDetail()
  }
})
//切换退款明细的显示状态
function toggleDetail(index) {
  // 如果当前显示的是这个索引，则隐藏它；否则显示它
  if (currentShowIndex.value === index) {
    // 隐藏当前显示的退款明细
    currentShowIndex.value = -1
  } else {
    // 显示指定索引的退款明细
    currentShowIndex.value = index
  }
}

// 检查指定索引的退款明细是否显示
function isDetailShown(index) {
  return currentShowIndex.value === index
}
</script>

<template>
  <view class="container">
    <!-- 状态条 -->
    <view class="state-text">{{ refundInfo.state_text }}</view>

    <!-- 退款金额明细 -->
    <MyCard title="退款金额明细">
      <view class="summary">
        <view>
          申请金额
          <view>
            {{ formatMoney(refundInfo.pre_refund_amount || 0) }}
            <!-- 这里的更多按钮有啥用？ -->
            <text v-if="false" class="iconfont icon-gengduo"></text>
          </view>
        </view>
        <view>
          实际退款金额
          <text>{{ refundInfo.state === 3 ? formatMoney(refundInfo.refund_amount || 0) : '-' }}</text>
        </view>
        <view v-if="refundInfo.state === 3" class="reason">
          {{ refundInfo.state_text }}
        </view>
      </view>
    </MyCard>

    <view class="title data">申请详情</view>
    <view class="list">
      <MyCard v-for="(item, index) in refundInfo.details || []" :key="index" customClass="refund-detail-card">
        <view class="refund-item">
          <view class="item-info">
            <image :src="item.pic" mode="aspectFill" />
            <view class="right">
              <view class="name">{{ item.product_name || '' }}</view>
              <view class="sku-name">{{ item.sku_name || '' }}</view>
              <view class="day-quantity">
                <!-- 酒店日期显示 -->
                <view v-if="item.order_sub_type === 3" class="hotel-day">
                  <view>
                    <text class="weekday">{{ formatTime(item.date_start, 'ddd') }}</text>
                    <text>{{ formatTime(item.date_start) }}</text>
                  </view>
                  <view>
                    <text class="weekday">{{ formatTime(item.date_end, 'ddd') }}</text>
                    <text>{{ formatTime(item.date_end) }}</text>
                  </view>
                </view>
                <!-- 门票日期显示 -->
                <view v-if="item.order_sub_type === 2" class="ticket-day">
                  {{ formatTime(item.date_start, 'YYYY-MM-DD dddd') }}
                </view>
                <!-- 团游日期显示 -->
                <view v-if="item.order_sub_type === 1">
                  团期：{{ formatTime(item.date_start, 'YYYY-MM-DD dddd') }}
                </view>
              </view>
            </view>
            <view class="quantity">数量：{{ item.refund_quantity || 1 }}{{ item.order_sub_type === 3 ? '间' : '件' }}</view>
          </view>
          <view class="service-process">
            <view class="title">服务进度：</view>
            <view class="line">
              <text class="refund-state do">申请售后</text>
              <text class="iconfont icon-gengduo icon_do"></text>
              <text :class="`refund-state ${item.state >= 1 ? 'do' : ''}`">平台审核</text>
              <text :class="`iconfont icon-gengduo ${item.state >= 1 ? 'icon_do' : ''}`"></text>
              <text :class="`refund-state ${item.state >= 2 ? 'do' : ''}`">{{ item.state == 2 ? '拒绝' : '退款' }}</text>
              <text :class="`iconfont icon-gengduo ${item.state >= 2 ? 'icon_do' : ''}`"></text>
              <text :class="`refund-state ${item.state == 5 ? 'do' : ''}`">完成</text>
            </view>
            <view v-if="item.state != 5" class="reason">
              {{ item.state_text }}
            </view>
            <view v-else class="reason complete">
              退款已完成，退款金额：
              <text class="refund-amount">{{ formatMoney(item.refund_amount || 0) }}</text>

              <view class="tooltip-container">
                <!-- 退款明细信息框 -->
                <view v-if="currentShowIndex === index" class="refund-detail-box">
                  <view class="refund-tips-box">
                    <view>申请退款金额
                      <text>{{ formatMoney(item.pre_refund_amount || 0) }}</text>
                    </view>
                    <view v-if="item.penalty_amount > 0">平台罚金
                      <text>{{ formatMoney(item.penalty_amount || 0) }}</text>
                    </view>
                    <view>实际到账金额
                      <text>{{ formatMoney(item.refund_amount || 0) }}</text>
                    </view>
                  </view>
                  <view class="refund-tips-arrow"></view>
                </view>

                <!-- 切换按钮 -->
                <MyButton @tap="toggleDetail(index)" size="mini" type="primary">
                  {{ isDetailShown(index) ? '点击关闭' : '查看明细' }}
                </MyButton>
              </view>
            </view>
          </view>
        </view>
      </MyCard>
    </view>

  </view>
  <MyCustomerService />
</template>

<style lang="scss" scoped>
@import "refund";

:deep(.refund-detail-card .content) {
  /* 只针对最后一个MyCard组件的样式 */
  padding-top: 0 !important;
  display: flex;
}
</style>