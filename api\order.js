import request from '../utils/request.js'

export function orderCancel(id) {
  return request({
    url: '/api/v1/front/order/cancel',
    method: 'post',
    data: {
      id
    },
  })
}

export function orderPreview(data) {
  return request({
    url: '/api/v1/front/order/preorder',
    method: 'post',
    data,
  })
}

export function orderDetail(id) {
  return request({
    url: '/api/v1/front/order/detail',
    method: 'get',
    data: {
      id
    },
  })
}

export function orderList(data) {
  return request({
    url: '/api/v1/front/order/list',
    method: 'get',
    data,
  })
}

export function orderPayConfirm(data) {
  return request({
    url: '/api/v1/front/order/payconfirm',
    method: 'post',
    data,
  })
}

export function orderPay(data) {
  return request({
    url: '/api/v1/front/order/pay',
    method: 'post',
    data,
  })
}

export function orderSubmit(data) {
  return request({
    url: '/api/v1/front/order/submit',
    method: 'post',
    data,
  })
}

export function getCanRefundList(order_id) {
  return request({
    url: '/api/v1/front/order/canrefunds',
    method: 'get',
    data:{order_id},
  })
}



export function applyRefund(data) {
  return request({
    url: '/api/v1/front/order/applyrefund',
    method: 'post',
    data,
  })
}

/**
 * 获取退款详情
 * @param {Object} params 包含order_id的参数对象
 * @returns {Promise<*>}
 */
export function getRefundDetail(params) {
  return request({
    url: '/api/v1/front/order/refunddetail',
    method: 'get',
    data: params,
  })
}

export function getRefundOrderList(data) {
  return request({
    url: '/api/v1/front/order/refundlist',
    method: 'get',
    data,
  })
}