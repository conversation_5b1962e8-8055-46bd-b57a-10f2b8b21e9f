/* 翻转卡片的基本样式 */
.flip-card {
  background-color: transparent;
  width: 100%;
  perspective: 1000px; /* 3D效果的视角 */
  position: relative;
}

/* 内部容器，用于实现3D翻转效果 */
.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transform-style: preserve-3d; /* 保持3D效果 */
  transform-origin: center center; /* 确保旋转和缩放以元素中心为基准 */
}

/* 正面和背面的共同样式 */
.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden; /* 隐藏背面 */
  overflow: hidden; /* 防止内容溢出 */
  transform-origin: center center; /* 确保旋转和缩放以元素中心为基准 */
}

/* 正面默认显示 */
.flip-card-front {
  z-index: 2;
}

/* 背面默认隐藏（翻转180度） */
.flip-card-back {
  transform: rotateY(180deg);
  position: relative; /* 为遮罩层提供定位上下文 */
}

/* 背面内容容器 */
.flip-card-back-content {
  width: 100%;
  height: 100%;
  opacity: 1;
  transition: opacity 1s ease; /* 增加过渡时间，使渐显效果更明显 */
}

/* 刮刮乐启用时，背面内容初始隐藏 */
.scratch-enabled .flip-card-back-content {
  opacity: 0;
}

/* 背面内容显示时的样式 */
.scratch-enabled .flip-card-back-content.content-visible {
  opacity: 1;
}

/* 背面内容渐显动画 */
@keyframes contentFadeIn {
  0% {
    opacity: 0;
  }
  30% {
    opacity: 0.2;
  }
  60% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 正在渐显的背面内容 */
.scratch-enabled .flip-card-back-content.content-fading-in {
  animation: contentFadeIn var(--scratch-duration) ease forwards;
}

/* 遗置层隐藏时的样式 */
.scratch-mask.hidden {
  opacity: 0;
  pointer-events: none; /* 禁用遗置层的交互，确保它不会阻挡背面内容的点击 */
}

/* 垂直方向的背面 */
.flip-card[direction="vertical"] .flip-card-back {
  transform: rotateY(180deg);
}

/* 简单动画类型 - 水平翻转效果 */
.flip-card.animation-type-simple.flipped .flip-card-inner {
  transform: rotateY(180deg);
}

/* 简单动画类型 - 垂直翻转效果 */
.flip-card.animation-type-simple[direction="vertical"] .flip-card-back {
  transform: rotateY(180deg);
}

.flip-card.animation-type-simple[direction="vertical"].flipped
  .flip-card-inner {
  transform: rotateY(180deg);
}

/* 粒子消散遮罩层样式 */
.scratch-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7); /* 降低透明度，使遮罩层不那么突兀 */
  z-index: 10;
  transform-origin: center center;
  overflow: hidden;
  border-radius: 16rpx; /* 添加圆角，与卡片保持一致 */
  transition: opacity 0.2s ease; /* 添加过渡效果，使遮罩层出现和消失更加平滑 */
}

/* 粒子样式 */
.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 第一圈粒子 */
.particle-1 {
  top: calc(50% - 30rpx);
  left: calc(50% - 30rpx);
  background-color: rgba(255, 255, 255, 0.8);
  width: 10rpx;
  height: 10rpx;
}

.particle-2 {
  top: calc(50% - 30rpx);
  left: calc(50% + 30rpx);
  background-color: rgba(255, 255, 255, 0.8);
  width: 10rpx;
  height: 10rpx;
}

.particle-3 {
  top: calc(50% + 30rpx);
  left: calc(50% - 30rpx);
  background-color: rgba(255, 255, 255, 0.8);
  width: 10rpx;
  height: 10rpx;
}

.particle-4 {
  top: calc(50% + 30rpx);
  left: calc(50% + 30rpx);
  background-color: rgba(255, 255, 255, 0.8);
  width: 10rpx;
  height: 10rpx;
}

/* 第二圈粒子 */
.particle-5 {
  top: calc(50% - 80rpx);
  left: calc(50% - 80rpx);
  background-color: rgba(255, 255, 255, 0.7);
  width: 8rpx;
  height: 8rpx;
}

.particle-6 {
  top: calc(50% - 80rpx);
  left: calc(50% + 80rpx);
  background-color: rgba(255, 255, 255, 0.7);
  width: 8rpx;
  height: 8rpx;
}

.particle-7 {
  top: calc(50% + 80rpx);
  left: calc(50% - 80rpx);
  background-color: rgba(255, 255, 255, 0.7);
  width: 8rpx;
  height: 8rpx;
}

.particle-8 {
  top: calc(50% + 80rpx);
  left: calc(50% + 80rpx);
  background-color: rgba(255, 255, 255, 0.7);
  width: 8rpx;
  height: 8rpx;
}

/* 第三圈粒子 - 彩色 */
.particle-9 {
  top: calc(50% - 120rpx);
  left: calc(50% - 50rpx);
  background-color: rgba(255, 100, 100, 0.6);
  width: 6rpx;
  height: 6rpx;
}

.particle-10 {
  top: calc(50% - 120rpx);
  left: calc(50% + 50rpx);
  background-color: rgba(100, 255, 100, 0.6);
  width: 6rpx;
  height: 6rpx;
}

.particle-11 {
  top: calc(50% + 50rpx);
  left: calc(50% - 120rpx);
  background-color: rgba(100, 100, 255, 0.6);
  width: 6rpx;
  height: 6rpx;
}

.particle-12 {
  top: calc(50% + 50rpx);
  left: calc(50% + 120rpx);
  background-color: rgba(255, 255, 100, 0.6);
  width: 6rpx;
  height: 6rpx;
}

/* 粒子消散动画关键帧定义 - 使用小程序兼容的属性，增加中间关键帧使动画更加平滑 */
@keyframes particleAnimation {
  0% {
    transform: scale(0.1);
    opacity: 0.3;
  }
  10% {
    transform: scale(0.5);
    opacity: 1;
  }
  40% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  70% {
    transform: scale(2);
    opacity: 0.4;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* 背景消散动画 - 使用opacity替代background-color，提高兼容性，增加中间关键帧使动画更加平滑 */
@keyframes backgroundFade {
  0% {
    opacity: 1;
  }
  30% {
    opacity: 0.9;
  }
  60% {
    opacity: 0.6;
  }
  80% {
    opacity: 0.3;
  }
  100% {
    opacity: 0;
  }
}

/* 粒子消散动画样式 */
.scratch-mask.scratching {
  animation: backgroundFade var(--scratch-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

/* 粒子动画样式 */
.scratch-mask.scratching .particle {
  animation: particleAnimation var(--scratch-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  opacity: 0.3;
}

/* 错开动画时间，增强效果 - 调整延迟时间，使粒子消散更加自然 */
.scratch-mask.scratching .particle-1 {
  animation-delay: calc(var(--scratch-duration) * 0.02);
}

.scratch-mask.scratching .particle-2 {
  animation-delay: calc(var(--scratch-duration) * 0.05);
}

.scratch-mask.scratching .particle-3 {
  animation-delay: calc(var(--scratch-duration) * 0.08);
}

.scratch-mask.scratching .particle-4 {
  animation-delay: calc(var(--scratch-duration) * 0.12);
}

.scratch-mask.scratching .particle-5 {
  animation-delay: calc(var(--scratch-duration) * 0.15);
}

.scratch-mask.scratching .particle-6 {
  animation-delay: calc(var(--scratch-duration) * 0.18);
}

.scratch-mask.scratching .particle-7 {
  animation-delay: calc(var(--scratch-duration) * 0.22);
}

.scratch-mask.scratching .particle-8 {
  animation-delay: calc(var(--scratch-duration) * 0.25);
}

.scratch-mask.scratching .particle-9 {
  animation-delay: calc(var(--scratch-duration) * 0.28);
}

.scratch-mask.scratching .particle-10 {
  animation-delay: calc(var(--scratch-duration) * 0.32);
}

.scratch-mask.scratching .particle-11 {
  animation-delay: calc(var(--scratch-duration) * 0.35);
}

.scratch-mask.scratching .particle-12 {
  animation-delay: calc(var(--scratch-duration) * 0.38);
}

/* 多圈旋转动画 - 水平方向 */
@keyframes flipCardSpinHorizontal {
  0% {
    transform: rotateY(0);
  }
  70% {
    transform: rotateY(calc(var(--rotations) * 360deg));
  }
  100% {
    transform: rotateY(calc(var(--rotations) * 360deg + 180deg));
  }
}

/* 多圈旋转动画 - 垂直方向 */
@keyframes flipCardSpinVertical {
  0% {
    transform: rotateY(0);
  }
  70% {
    transform: rotateY(calc(var(--rotations) * 360deg));
  }
  100% {
    transform: rotateY(calc(var(--rotations) * 360deg + 180deg));
  }
}

/* 动画类名控制 */
.flip-card.animate-spin-horizontal .flip-card-inner {
  animation: flipCardSpinHorizontal var(--duration) forwards;
}

.flip-card.animate-spin-vertical .flip-card-inner {
  animation: flipCardSpinVertical var(--duration) forwards;
}

/* 为多圈垂直旋转添加背面内容修正 */
.flip-card.animate-spin-vertical.flipped .flip-card-back {
  transform: rotateY(180deg);
}

/* 缓动函数控制 */
.flip-card.animate-spin-horizontal .flip-card-inner,
.flip-card.animate-spin-vertical .flip-card-inner {
  animation-timing-function: var(--easing);
}
