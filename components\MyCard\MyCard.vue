<template>
  <view :class="customClass" class="my-card">
    <slot name="header">
      <view v-if="title.length" class="header">
        <view class="title">{{ title }}</view>
      </view>
    </slot>
    <view v-if="$slots.default" class="content">
      <slot></slot>
    </view>

  </view>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  customClass: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
@use '../../styles/define';
@use '../../styles/mix';

.my-card {
  border: 2rpx solid #EDEEF0;
  padding: 30rpx;
  border-radius: 16rpx;
  background: white;

  .header {
    .title {
      font-weight: bold;
    }
  }

  .content {
    padding-top: 30rpx;
  }
}
</style>
