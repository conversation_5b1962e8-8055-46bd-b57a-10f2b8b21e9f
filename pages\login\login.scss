@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.container {
  position: relative;
  min-height: 100vh;

  image {
    width: 100%;
  }
}

.top-bg {
  margin-top: -60rpx;
}

.center {
  margin-top: 5%;
  @include center(column);

  .h {
    @include center(column);
    gap: 20rpx;

    .avatar-wrapper {
      border: none;
      padding: 0;
      background: transparent;
      line-height: 0;
      border-radius: 0;
      margin: 0;
    }

    image {
      width: 120rpx;
      height: 120rpx;
    }

    text {
      color: #B7B9BD;
    }
  }

  .nickname {
    @include center();
    gap: 50rpx;
    margin: 40rpx 0;

    input {
      width: 200rpx;
    }
  }

  :deep(.login), :deep(.login .my-button) {
    padding-left: 172rpx;
    padding-right: 172rpx;
  }
}

.footer {
  @include center(column);
  gap: 40rpx;
  width: 100%;
  margin-top: 10%;

  image {
    width: 204rpx;
    height: 52rpx;
  }

  text {
    font-size: 34rpx;
  }
}