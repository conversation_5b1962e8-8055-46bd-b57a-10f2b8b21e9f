<template>
  <YjNavBar :custom-style="{ background: navBg }" @load="({height, paddingBottom}) => {
   pageStyle.paddingTop = `${height + paddingBottom}px`
  }">
    <template #left>
      <text class="iconfont icon-gengduo2" style="font-size:52rpx" @tap="showUCenter = true"></text>
    </template>
    <template #center>
      <view class="navbar-center" @tap="navTo('pages/option/cities/cities')">
        <text class="iconfont icon-daohangdizhiweizhi"></text>
        {{ location?.city }}
        <text class="iconfont icon-qiehuan1"></text>
      </view>
    </template>
  </YjNavBar>
  <view id="container" :style="pageStyle" class="container" @tap="globalStore.pageTap">
    <view>
      <view class="plan-tabs">
        <scroll-view :scroll-x="true" :show-scrollbar="false" enhanced>
          <view v-for="(item, index) in planTags" :key="index" :class="{
            active: activeTag === index
          }"
                :style="activeTag === index ? `background:${activeTagColor}` : ''"
                class="tab-item"
                @tap="onTapPlanTag(index)">{{
              item
            }}
          </view>
        </scroll-view>
      </view>

      <view class="recommend">
        <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/home/<USER>/index/list-item-ren.png"/>
        <view class="header">
          <view>{{ location.city }}·
            <text :style='`color:${recPlans.color}`'>{{ recPlans.title }}</text>
          </view>
          <view @tap="navTo('pages/plans/plans', {tag: recPlans.title})">查看更多</view>
        </view>
        <view class="list">
          <MyRecommendPlanItem v-for="(item, index) in recPlans.list" :key="item.ai_reqid" :itemIndex="index"
                               :plan="item"/>
        </view>
        <uni-load-more v-if="recPlans.list.length === 0" status="noMore"/>
      </view>

      <template v-for="(item,i) in banners">
        <image v-if="i===0" :src="item.pic" class="banner_img" mode="widthFix" @tap="onTapBanner(item)"/>
      </template>

      <view class="keywords">
        <view class="list">
          <view v-for="(item, index) in prompts" :key="index" @tap="onPrompt(item)">
            <text>🔎</text>
            {{ item }}
          </view>
        </view>
      </view>
    </view>

    <template v-for="(item, index) in activities" :key="index">
      <my-activity-popup v-if="!item.hidden" :mask-click="false" @close="() => onActivityClose(item)"
                         @do="() => onActivityDo(item)">
        <view class="activity-content">
          <image :src="item.pic" mode="widthFix"/>
        </view>
      </my-activity-popup>
    </template>

    <MyCustomerService/>

    <UCenterDrawer v-model="showUCenter" :reload="mineReload"/>

    <ImportTripPopup v-if="showImportPopup" @close="showImportPopup = false" @submit="handleImportSubmit"/>
    <view class="footer">
      <!-- 是否需要条件判断v-if="chatShow" -->
      <MyChatInputWithMenu :reinit="mineReload" @discover="onDiscover" @done="onQucik" @show-menu="show => {
        scrollToBottom('container')
      }" @search-scenic="onSearchScenic"
                           @search-hotel="onSearchHotel" @import-trip="onImportTrip"/>
    </view>

  </view>
</template>

<script setup>
import {computed, nextTick, ref, watch} from 'vue'
import {useGlobalStore} from '../../store/global'
import {planHistory} from '../../api/plan'
import {onLoad, onShareAppMessage, onShow} from '@dcloudio/uni-app'
import {navTo, scrollToBottom} from '../../utils'
import {index, plans} from '../../api'
import MyChatInputWithMenu from '@/components/MyChatInputWithMenu/MyChatInputWithMenu.vue'
import MyActivityPopup from "@/components/MyActivityPopup/MyActivityPopup.vue";
import {cacheGet, cacheSet} from "@/utils/cache";
import MyCustomerService from "@/components/MyCustomerService/MyCustomerService.vue";
import UCenterDrawer from "@/pages/index/components/UCenterDrawer.vue";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import ImportTripPopup from "@/components/ImportTripPopup/ImportTripPopup.vue";
import MyRecommendPlanItem from "@/components/MyRecommendPlanItem/MyRecommendPlanItem.vue";
import UniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
import {useUserStore} from "@/store/user";


const globalStore = useGlobalStore()
const userStore = useUserStore()
const list = ref([])
const isIng = ref(false)
const prompts = ref([])
const scrollIntoView = ref('')
const activities = ref([])
const showUCenter = ref(false)
const showImportPopup = ref(false)
const pageScroll = ref(0)
const mineReload = ref(0)
const navBg = computed(() => {
  const max = 30
  let st = pageScroll.value
  if (st >= max) {
    st = max
  }
  return `rgba(255,255,255, ${st / max})`
})
const pageStyle = ref({})
const location = computed(() => globalStore.location)
const recPlans = ref({
  title: '夏季精选',
  color: '',
  list: []
})
const planTags = ref([])
const activeTag = ref(0)
const activeTagColor = ref('')
const banners = ref([])

function onTapViewHistory() {
  if (list.value.length) {
    list.value = []
  } else {
    planHistory().then(({data}) => {
      list.value = data.list
      setScrollView()
    })
  }
}

watch(globalStore.location, () => {
  loadPlans()
})

function onTapPlanTag(index) {
  const tag = planTags.value[index]
  activeTag.value = index

  loadRecommends(tag)
}

async function loadRecommends(tag) {
  const params = {}
  if (tag) {
    params.hot_tag = tag
  }

  const {data} = await plans(params)
  data.list = data.list.slice(0, 3)

  Object.assign(recPlans.value, data)
}

function onTapBanner(item) {
  navTo(item.link)
}

function loadPlans() {
  index().then((res) => {
    const {data} = res

    prompts.value = data.prompts
    activities.value = data.activities
    planTags.value = data.hot_tags
    activeTagColor.value = data.title_color
    banners.value = data.banners

    for (const item of data.activities) {
      const view = cacheGet(`activity_view_${item.uuid}`)
      item.hidden = !!view
    }

    loadRecommends()
  })
}

function onActivityDo(item) {
  navTo('pages/activity/activity', {code: item.uuid})
}

function onActivityClose(item) {
  cacheSet(`activity_view_${item.uuid}`, true)
  item.hidden = true
}

function setScrollView() {
  nextTick(() => {
    scrollIntoView.value = `plan-${list.value.length - 1}`
  })
}

async function onPrompt(prompt) {
  navTo('pages/chat/chat', {text: prompt})
}

function onQucik(params) {
  // 支持直接传入字符串作为text参数（向后兼容）
  if (typeof params === 'string') {
    // 从globalStore中获取context_id
    const storedContextId = globalStore.getContextId()
    navTo('pages/chat/chat', {text: params, context_id: storedContextId})
    return
  }

  // 支持传入对象参数
  let {text, url = '', context_id = ''} = params

  // 如果没有传入context_id，则从globalStore中获取
  if (!context_id) {
    context_id = globalStore.getContextId()
  }

  navTo('pages/chat/chat', {text, url, context_id})
}

function onSearchScenic() {
  navTo('pages/search/search', {tab: 'scenic'})
}

function onSearchHotel() {
  navTo('pages/search/search', {tab: 'hotel'})
}

function onDiscover() {
  navTo('pages/discover/discover')
}

function onImportTrip() {
  showImportPopup.value = true
}

function handleImportSubmit(text) {
  if (text) {
    // 使用正则表达式匹配URL，确保包含#符号及其后面的部分
    const urlRegex = /(?:(?:https?|ftp):\/\/)?(?:www\.)?(?:[-a-zA-Z0-9@:%._\+~#=]{1,256}\.)+[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)(?:#[-a-zA-Z0-9()@:%_\+.~#?&//=]*)?/gi;
    const matches = text.match(urlRegex);

    let content;
    let url = '';

    // 如果找到URL，则使用第一个URL
    if (matches && matches.length > 0) {
      url = decodeURIComponent(matches[0]);
      content = `${url}`;
    } else {
      // 如果不包含URL且文本长度超过50个汉字，则截取前50个汉字
      const chineseChars = text.match(/[\u4e00-\u9fa5]/g);
      if (chineseChars && chineseChars.length > 50) {
        // 找出前50个汉字在原文中的位置
        let count = 0;
        let endIndex = 0;
        for (let i = 0; i < text.length; i++) {
          if (/[\u4e00-\u9fa5]/.test(text[i])) {
            count++;
            if (count === 50) {
              endIndex = i + 1;
              break;
            }
          }
        }
        content = text.substring(0, endIndex);
      } else {
        content = text;
      }
    }
    // 使用新的对象参数格式调用onQucik
    // 如果URL存在，则不设置text，让onQucik方法处理
    if (url && url.length > 0) {
      onQucik({
        url: url,
        text: `帮我解析行程：${url}`
      })
    } else {
      onQucik({
        text: `${content}`
      })
    }
  }
  showImportPopup.value = false
}

onShareAppMessage(() => {
  const opt = {
    title: '义伴AI-做更懂用户的旅行定制平台',
    path: 'pages/index/index'
  }
  if (userStore.isLogin) {
    opt.path += `?share_uid=${userStore.userInfo.user_id}`
  }

  return opt
})

onShow(async () => {
  isIng.value = false
  // chatShow.value = true
  mineReload.value++
})

onLoad((query = {}) => {
  const {
    expand_history = '',
    ucenter = '',
    share_uid = ''
  } = query
  if (expand_history) {
    setTimeout(onTapViewHistory, 1000)
  }
  if (ucenter) {
    showUCenter.value = true
  }

  globalStore.getLocation().then(() => loadPlans())
  globalStore.shareUid = share_uid
})

</script>

<style lang="scss" scoped>
@import 'index.scss';
</style>
