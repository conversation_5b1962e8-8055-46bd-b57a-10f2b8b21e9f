@import '../../../styles/_mix.scss';
@import '../../../styles/_wifi.scss';

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
}

.card-container, .data {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-container {
  padding-top: 114rpx;

  .center {
    @include center(column);

    .product-name, .code {
      border-radius: 8rpx;
    }

    .product-name {
      @include center(cloumn);
      padding: 6rpx 12rpx;
      gap: 10rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }

      border: 2rpx solid #FBD35A;
    }

    .code {
      margin: 42rpx 0 19rpx 0;
      background: #FBD35A;
      padding: 16rpx 64rpx;
    }

    .expire {
      color: #999999;
      font-size: 20rpx;
    }
  }

  .data {
    margin-top: 108rpx;

    :deep(.desc-box) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

}