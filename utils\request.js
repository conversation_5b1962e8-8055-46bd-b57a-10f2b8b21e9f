import { navTo, showToast } from ".";
import { useGlobalStore } from "../store/global";
import { useUserStore } from "../store/user";

export function appendGlobalData(config) {
  const userStore = useUserStore();
  const globalStore = useGlobalStore();
  if (!config.data) {
    config.data = {};
  }

  Object.assign(config.data, getGlobalData());

  if (userStore.token) {
    config.header["token"] = userStore.token;
  }
}

export function getGlobalData() {
  const globalStore = useGlobalStore();

  const data = {
    channel: globalStore.channel,
  };

  if (globalStore.location.lng) {
    Object.assign(data, {
      lng: globalStore.location.lng,
      lat: globalStore.location.lat,
      zone_id: globalStore.location.zone_id,
    });
  }

  return data;
}

export default function request(config, loading = true, showErrorMsg = true) {
  return new Promise((resolve, reject) => {
    const whitelist = ["/user/center"];

    if (loading) {
      uni.showLoading({
        title: "请求中...",
      });
    }

    config.timeout = 600000;
    config.method = config.method.toUpperCase();

    config.header = Object.assign({}, config.header);
    config.url = import.meta.env.VITE_API_BASE_URL + config.url;
    if (config.method === "POST") {
      config.header["content-type"] = "application/x-www-form-urlencoded";
    }
    appendGlobalData(config);

    config.success = (res) => {
      const { data, statusCode } = res;
      if (statusCode !== 200) {
        showToast(`网络错误${data}`, "error").then(reject);
        return;
      }
      if (data.code === 0) {
        resolve(data);
      } else {
        if (data.code === 900000) {
          const index = whitelist.findIndex((item) =>
            config.url.includes(item)
          );
          if (index > -1) {
            return reject(data);
          }

          showToast(data.msg).then(() => {
            navTo("pages/login/login");
          });
        } else {
          if (showErrorMsg) {
            showToast(data.msg).then(() => {
              reject(data);
            });
          } else {
            reject(data);
          }
        }
      }
    };
    config.complete = () => {
      if (loading) {
        uni.hideLoading();
      }
    };

    config.fail = (res) => {
      console.log(res);
      if (showErrorMsg) {
        showToast(`网络错误`, "error");
      }
    };

    return uni.request(config);
  });
}
