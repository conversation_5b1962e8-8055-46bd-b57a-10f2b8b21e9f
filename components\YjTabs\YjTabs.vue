<template>
  <view :class="customClass" class="yj-tabs">
    <view v-for="(item, index) in data" :key="index" :class="{
			active: value == item.value
		}" :style="itemStyle(item)" class="tabs-item" @tap="onTap(item)">
      <slot :item="item">
        {{ item.label }}
        <view :style="{
				background: item.value == value ? activeColor : ''
			}" class="line"></view>
      </slot>
    </view>
  </view>
</template>

<script setup>

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  customClass: { // 自定义样式类
    type: String,
    default: ''
  },
  activeColor: {
    type: String,
    default: '#2DCC4E'
  },
  textColor: {
    type: String,
    default: '#333'
  }
})
const value = defineModel()
const itemStyle = (item) => {
  const s = {
    color: props.textColor
  }
  if (value.value == item.value) {
    s.color = props.activeColor
  }

  return s
}

function onTap(item) {
  value.value = item.value
}
</script>

<style scoped>
.yj-tabs,
.tabs-item {
  display: flex;
}

.yj-tabs {
  justify-content: space-between;
  width: 100%;
}

.tabs-item {
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding-top: 10rpx;
  /* font-size: 28rpx; */
}

.tabs-item.active {
  font-weight: 500;
}

.line {
  height: 5rpx;
  width: 50%;
  content: ' ';
  display: block;
  border-radius: 12rpx;
}
</style>
