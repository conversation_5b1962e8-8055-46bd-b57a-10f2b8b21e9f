<style lang="scss" scoped>
@import "./SelectRefundSubOrder.scss";

/* 覆盖第三方组件的样式，而不是修改组件代码 */
:deep(.uni-popup .uni-transition[name="mask"]) {
  touch-action: none !important;
  pointer-events: auto !important; /* 确保可以接收事件 */
}

:deep(.uni-popup__wrapper) {
  z-index: 1000 !important; /* 确保弹窗内容区域在遮罩层之上 */
}

.sub-orders {
  max-height: 60vh;
  overflow-y: auto;
  /* 允许垂直滚动，但防止水平滑动穿透 */
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 10; /* 确保内容可以点击 */
}

/* 确保复选框和其他内容可以被点击 */
:deep(.custom-checkbox),
:deep(.item-pic),
:deep(.item-details),
:deep(.select-quantity-box) {
  pointer-events: auto !important;
  position: relative;
  z-index: 20;
}
</style>
<script setup>

import {computed, onMounted, ref, watch} from "vue";
import MyInfoPopup2 from "@/components/MyInfoPopup2/MyInfoPopup2.vue";
import MyButton from "@/components/MyButton/MyButton.vue";
import YjCheckboxGroup from "@/components/YjCheckbox/YjCheckboxGroup.vue";
import YjCheckbox from "@/components/YjCheckbox/YjCheckbox.vue";
import dayjs from "dayjs";
import 'dayjs/locale/zh-cn';
import {deepToRaw, formatMoney, navTo, showToast} from "@/utils";
import MyInputNumber from "@/components/MyInputNumber/MyInputNumber.vue";
import {getCanRefundList, applyRefund} from "@/api/order";
import {
  OrderSubTypeHotel,
  OrderSubTypeTicket,
  OrderSubTypeTuan, OrderTypeHotel, OrderTypeTicket, OrderTypeTravel, OrderTypeTuan
} from "@/utils/constmap";

const emit = defineEmits(['close'])
const showPopup = ref(true) // 控制弹窗显示状态

// 关闭弹窗方法
function closePopup() {
  showPopup.value = false
  emit('close')
}

const props = defineProps({
  orderId: {
    type: String,
    required: true
  },
})
dayjs.locale('zh-cn')
const list = ref([])
const loading = ref(false)
const selectedList = computed(() => list.value.filter(v => v.select_quantity > 0))

// 计算各类型选中的数量
const selectedCounts = computed(() => {
  const counts = {};

  [0, OrderSubTypeHotel,OrderSubTypeTicket,OrderSubTypeTuan].forEach(t => {
    counts[t] = {
      count:0,
      amount:0,
    }
  })

  list.value.forEach((item, index) => {
    let v  = counts[item.order_sub_type]
    v.count += item.select_quantity
    v.amount += item.select_quantity * item.sku_refund_price
    counts[0].count += v.count
    counts[0].amount += v.amount
  })

  return counts
})

// 根据订单类型获取相应的文本
function getUnit(type) {
  switch (Number(type)) {
    case OrderSubTypeHotel:
      return '间'
    case OrderSubTypeTicket:
      return '张'
    case OrderSubTypeTuan:
      return '位'
    default:
      return '位'
  }
}
function getTypeText(type) {
  switch (Number(type)) {
    case OrderSubTypeHotel:
      return '酒店'
    case OrderSubTypeTicket:
      return '门票'
    case OrderSubTypeTuan:
      return '团游'
    default:
      return '数量'
  }
}


onMounted(() => {
  fetchRefundList()
})

// 提交退款申请
function submitRefund() {
  if (selectedList.value.length === 0) {
    uni.showToast({
      title: '请选择要退款的订单',
      icon: 'none'
    })
    return
  }

  // 构建退款详情
  const refundDetails = selectedList.value.map(v => ({
    order_detail_id: v.id,
    quantity: v.select_quantity
  }))

  // 调用退款接口
  uni.showLoading({title: '提交中...'})

  applyRefund({
    order_id: props.orderId,
    details: JSON.stringify(refundDetails)
  }).then(async (res) => {
    if (res.code === 0) {
      await showToast('退款申请提交成功');
      // 关闭弹窗
      closePopup()
      // 跳转到订单页的售后标签
      navTo('/pages/orders/orders?type=refund', {}, true);
    } else {
      await showToast(res.msg || '退款申请失败');
    }
  }).catch(async err => {
    console.error('退款申请失败', err);
    await showToast('退款申请失败');
  }).finally(() => {
    uni.hideLoading()
  })
}

// 阻止滚动事件向下层传递
function preventScroll(e) {
  // 只阻止滚动事件，不阻止点击事件
  if (e.type === 'touchmove' || e.type === 'wheel') {
    if (e.cancelable) {
      e.preventDefault();
    }
    e.stopPropagation();
  }
  return false;
}

function fetchRefundList() {
  loading.value = true
  getCanRefundList(props.orderId).then(res => {
    if (res.data && res.data.list && Array.isArray(res.data.list)) {
      // 正确获取嵌套在data.list中的子订单列表
      list.value = res.data.list.map(item => {
        item = {
          ...item,
          id: item.order_detail_id,
          pic: item.pic,
          name: item.product_name,
          start: item.date_start,
          end: item.date_end,
          peoples: item.peoples || [],
          select_quantity: 0,
          refund_rules: item.refund_rules,
          refund_rules_expand:false,
          attrs: [item.sku_name],
        }
        const dayStr  = dayjs(item.date_start).format('MM月DD日')
        if (item.order_sub_type===OrderSubTypeHotel) {
          item.attrs.push(`${dayStr}入住`)
        }else if (item.order_sub_type===OrderSubTypeTicket) {
          item.attrs.push(`${dayStr}使用`)
        }else  {
          item.attrs.push(`${dayStr}使用`)
        }
        return item
      })

      // 如果没有数据，显示提示信息
      if (list.value.length === 0) {
        uni.showToast({
          title: '没有可退款的订单',
          icon: 'none'
        })
      }
    } else {
      console.log('返回数据结构不符合预期', res.data)
      uni.showToast({
        title: '获取退款订单数据异常',
        icon: 'none'
      })
    }
  }).catch(err => {
    console.error('获取可退款列表失败', err)
    uni.showToast({
      title: '获取退款订单失败',
      icon: 'none'
    })
  }).finally(() => {
    loading.value = false
  })
}

</script>

<template>
  <!-- 微信小程序使用page-meta元素 -->
  <!-- #ifdef MP-WEIXIN -->
  <page-meta :page-style="'overflow:' + (showPopup ? 'hidden' : 'visible')"></page-meta>
  <!-- #endif -->

  <!-- 使用封装好的MyInfoPopup2组件 -->
  <MyInfoPopup2 title="选择退款订单" @close="closePopup">
    <view class="sub-orders"
          @touchmove.prevent="preventScroll"
          @wheel.prevent="preventScroll"
          catchtouchmove="preventScroll">
      <view v-if="loading" class="loading">加载中...</view>
      <view v-else-if="list.length === 0" class="empty">暂无可退款订单</view>
      <view v-else v-for="(item, index) in list" :key="index" :class="{
          selected:item.select_quantity>0
        }" class="sub-order-item">
        <view class="right">
          <view class="info">
            <text :class="{
                'icon-chuang': item.order_sub_type === OrderSubTypeHotel,
                'icon-jingdian': item.order_sub_type === OrderSubTypeTicket,
                'icon-huowudui': item.order_sub_type === OrderSubTypeTuan,
              }" class="iconfont icon"></text>
            <view class="you">
            <view class="item-name">
              <view class="name">{{ item.sku_name }}</view>
            </view>
            <view class="item-details">
              <view  class="attrs">
                <text v-for="(attr, aI) in item.attrs" :key="aI">{{ attr }}{{aI<item.attrs.length-1 ? " | ": ''}}</text>
              </view>

              <view v-if="item.refund_rules.length>0" class="refund-rules">
                <view class="rules">
                  <view v-if="!item.refund_rules_expand" class="rule">{{item.refund_rules[0]}}</view>
                  <view v-else class="rule" v-for="(rule, i) in item.refund_rules" :key="i">{{rule}}</view>
                </view>
                <text v-if="item.refund_rules.length>1" class="iconfont icon-xiangxia expander"
                      :class="{expand:item.refund_rules_expand}"
                      @tap="item.refund_rules_expand=!item.refund_rules_expand"></text>
              </view>

              <view class="refund-amount">
                <view class="amount">
                  <text>预计每{{getUnit(item.order_sub_type)}}可退：</text>
                  <text class="yen">{{formatMoney(item.sku_refund_price)}}</text>
                </view>
                <view class="quantity">
                  <view>可退<text class="b">{{item.can_refund_quantity}}</text>{{getUnit(item.order_sub_type)}}，已退<text class="b">{{
                      item.quantity-item.can_refund_quantity}}</text>{{getUnit(item.order_sub_type)}}</view>
                </view>
              </view>
            </view>
            </view>
          </view>
          <view class="select-quantity-box">
            <text class="quantity-label">选择{{ getTypeText(item.order_sub_type) }}数</text>
            <MyInputNumber class='new_view' :class="{noselect:item.select_quantity===0}" v-model="item.select_quantity"
                           :min="0"
                           :max="item.can_refund_quantity || 1" :step="item.min_refund_quantity||1"/>
          </view>
        </view>
      </view>
    </view>

    <template #footer>
      <view class="footer-container">
        <view class="summary">
          <template v-for="(item , type ) in selectedCounts" :key="type">
            <view v-if="type>0" class="infos">
              <view class="label">
                {{getTypeText(type)}}：
                已选<text class="num">{{ item.count }}</text>{{getUnit(type)}}
              </view>
              <view class="amount">{{formatMoney(item.amount)}}</view>
            </view>
          </template>
        </view>
        <view class="bottom">
          <view class="left">
            <view class="total">
              <text>合计退款：</text>
              <text class="amount">{{formatMoney(selectedCounts[0].amount)}}</text>
            </view>
          </view>
          <view class="right">
            <MyButton custom-class="contact-btn" open-type="contact">联系客服</MyButton>
            <MyButton custom-class="submit-btn" type="primary" :disable="selectedList.length === 0" @tap="submitRefund">提交退款</MyButton>
          </view>
        </view>

      </view>
    </template>
  </MyInfoPopup2>
</template>
