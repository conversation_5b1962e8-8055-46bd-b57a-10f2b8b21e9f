@import '../../../styles/_define.scss';
@import '../../../styles/_mix.scss';

.list {
  @include center(column);
  align-items: flex-start;
  gap: $padding-v2;
  padding: $padding-page;

  .list-item {
    image {
      width: 83rpx;
      height: 83rpx;
      border-radius: 50%;
    }

    width: 100%;
    border-radius: $padding-small +7rpx;
    @include center();
    justify-content: space-between;
    padding: $padding-page;
    align-items: stretch;

    &:nth-child(odd) {
      background: #C2AE0C;
      color: #FACA14;
    }

    &:nth-child(even) {
      background: #CDD4E2;
      color: white;
    }

    .left {
      @include center();
      justify-content: flex-start;
      gap: $padding-page;

      >view {
        @include center(column);
        gap: $padding-small;
        align-items: flex-start;

        .name {
          color: white;
          font-weight: 600;
        }
      }
    }

    .code,
    .state {
      color: white;
    }

    .right {
      @include center(column);
      align-items: flex-end;
      justify-content: space-between;

      >view {
        &:first-child {
          @include center();
          gap: $padding-small;
        }
      }

      .code {
        text {
          margin-left: $padding-small;
          font-weight: 600;
        }
      }
    }
  }
}