<template>
  <view class="my-content-renderer">
    <!-- 空状态显示 -->
    <view v-if="currentEmptyState" class="empty-state">
      <view class="empty-content">
        <view class="empty-icon">
          <text :class="['iconfont', currentEmptyState.icon]" />
        </view>
        <text class="empty-title">{{ currentEmptyState.title }}</text>
        <text class="empty-description">{{ currentEmptyState.description }}</text>
      </view>
    </view>
    
    <!-- 正常文本内容显示 -->
    <view v-else>
      <rich-text :nodes="richtext(parseMarkdown(text))" />
      
      <!-- 快速操作按钮 -->
      <view v-if="showFastTips && !isIng && isLastMessage" class="fast-tips">
        <view class="force" @tap="onForceSubmit">
          <text class="iconfont icon-fasong3"></text>
          直接生成
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { richtext } from '@/utils'
import { parseMarkdown } from '@/utils/markdown'

// 定义Props
const props = defineProps({
  // 要显示的文本内容
  text: {
    type: String,
    default: ''
  },
  // 空状态配置数组
  emptyConfigs: {
    type: Array,
    default: () => []
  },
  // 是否显示快速操作按钮
  showFastTips: {
    type: Boolean,
    default: false
  },
  // 是否为最后一条消息
  isLastMessage: {
    type: Boolean,
    default: false
  },
  // 是否正在处理中
  isIng: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['forceSubmit'])

// 计算当前的空状态配置
const currentEmptyState = computed(() => {
  if (!props.text || props.emptyConfigs.length === 0) {
    return null
  }
  
  // 查找匹配的空状态配置
  const matchedConfig = props.emptyConfigs.find(config => {
    return config.keywords.some(keyword => props.text.includes(keyword))
  })
  
  return matchedConfig || null
})

// 处理强制提交事件
function onForceSubmit() {
  emit('forceSubmit')
}
</script>

<style lang="scss" scoped>
@import 'my-content-renderer';
</style> 