<script setup>
import {computed, ref} from 'vue'

const props = defineProps({
  buttons: {
    type: Array,
    default: () => ([]),//{key:'btn1', angle:90}
  },
  radius: {//半径
    type: Number,
    default: 100,
  },
  show: {
    type: Boolean,
    default: false,
  },
})

const percent = ref(100)

let hideStyle = {
  left: 0,
  top: 0,
  transform: 'scale(0.0)',
}
const btns = computed(() => {
  let radius = props.radius
  let pt = percent.value
  let show = props.show
  return props.buttons.map((item, index) => {
    let style = {
      ...radiusStyle(radius, pt, item.angle || 0),
    }
    if (!show) {
      Object.assign(style, hideStyle)
    }
    return {
      ...item,
      style,
    }
  })
})

const radiusStyle = (radius, percent = 100, angle = 0) => {
  const radians = angle * Math.PI / 180 //弧度
  let sty = {
    left: `${(radius * percent * Math.cos(radians) / 100).toFixed(2)}px`,
    top: `${(radius * percent * Math.sin(radians) / 100).toFixed(2)}px`,
  }
  return sty
}
</script>
<template>
  <view class="float-button">
    <view class="fbutton" v-for="(item, index) in btns" :style="item.style">
      <slot name="btn" :item="item" :index="index"></slot>
    </view>
    <view class="wrapper">
      <slot/>
    </view>
  </view>
</template>
<style lang="scss" scoped>

.float-button {
  position: relative;
  overflow: visible;

  .fbutton {
    position: absolute;
    z-index: 1;
    transform: scale(1);
    transition: all 0.5s ease;
  }

  .wrapper {
  }
}
</style>