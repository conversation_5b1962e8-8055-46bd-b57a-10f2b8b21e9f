<script setup>

import Yj<PERSON>avB<PERSON> from "@/components/YjNavBar/YjNavBar.vue";
import { onLoad } from "@dcloudio/uni-app";
import { usercenter } from "@/api";
import { computed, ref } from "vue";
import { useGlobalStore } from "@/store/global";
import { bindInvite } from "@/api/user";
import { copyText, navBack, showToast, getCdnUrl } from "@/utils";

const userInfo = ref({})
const globalStore = useGlobalStore()

const pageStyle = ref({})
const inviteCodePoints = ref(globalStore.bindInviteGive)

function onNavLoad({ height, paddingBottom }) {
  pageStyle.value.paddingTop = `${height + paddingBottom}px`
}

const btnImage = computed(() => {
  if (userInfo.value.bind_invite_code && userInfo.value.can_bind_invite) {
    return getCdnUrl('/static/mine/btn-send-active.png')
  } else {
    return getCdnUrl('/static/mine/btn-send-deactive.png')
  }
})

function onBind() {
  if (!userInfo.value.can_bind_invite || !userInfo.value.bind_invite_code?.trim()) {
    return
  }
  bindInvite(userInfo.value.bind_invite_code.trim()).then(() => {
    showToast('绑定成功').then(() => navBack())
  })
}

function getUserInfo() {
  usercenter().then(({ data }) => {
    Object.assign(userInfo.value, data)
  })
}

onLoad(() => {
  getUserInfo()
})

</script>

<template>
  <YjNavBar :custom-style="{
    background: 'transparent'
  }" @load="onNavLoad" />
  <view :style="pageStyle" class="container">
    <view class="data">
      <view class="my">
        <view @tap="copyText(userInfo.invite_code)">
          我的邀请码：{{ userInfo.invite_code }}
          <text class="copy iconfont icon-fuzhi" />
        </view>
        <view>快把邀请码发给好友，邀请小伙伴一起旅游吧</view>
      </view>
      <view class="input-data">
        <view class="label">{{ userInfo.can_bind_invite ? '输入邀请码' : '我绑定的邀请码' }}
          <text>（领取积分）</text>
        </view>
        <view class="input">
          <input :disabled="!userInfo.can_bind_invite" :placeholder="`输入好友邀请码兑换${inviteCodePoints}积分`"
            :value="userInfo.bind_invite_code" @confirm="onBind"
            @input="({ detail: { value } }) => userInfo.bind_invite_code = value" />
          <image :src="btnImage" mode="widthFix" @tap="onBind" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use "../../styles/define";
@use "../../styles/mix";

.container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0)),
    linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0)),
    linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1)) !important;

  padding: define.$padding-page;
}

.data {
  margin-top: 60rpx;

  .my {
    margin-bottom: 120rpx;

    view {
      &:first-child {
        font-weight: bold;
      }

      &:last-child {
        margin-top: 10rpx;
        font-size: 20rpx;
        color: #999999;
      }
    }

    .copy {
      color: #1890FF;
    }
  }

  .input-data {
    .label {
      font-weight: bold;
      margin-bottom: 10rpx;

      text {
        font-size: 20rpx;
        color: #999999;
      }
    }

    .input {
      width: 560rpx;
      @include mix.center();
      justify-content: flex-start;
      background: #F7F7F9;
      padding: 16rpx 20rpx;
      border-radius: 8rpx;

      input {
        font-size: 20rpx;
        flex: 1;
      }

      image {
        width: 52rpx;
        height: 52rpx;
      }

    }

  }
}
</style>
