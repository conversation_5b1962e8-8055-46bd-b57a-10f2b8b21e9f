@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.container {
  min-height: 100vh;
  position: relative;
  @include linear-gradient();

  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.banner_img {
  width: 694rpx;
  height: auto;
  margin: 26rpx 28rpx 0;
  border-radius: 20rpx;
}

.plan-tabs, .recommend {
  margin: 30rpx $padding-page 0 $padding-page;
}

.plan-tabs {
  scroll-view {
    white-space: nowrap;
    overflow: auto;
    display: flex;
  }

  .tab-item {
    display: inline-block;
    border-radius: 32rpx;
    padding: 12rpx 34rpx;
    background: #FFF7D4;
    color: #E6CC86;
    margin-right: 20rpx;

    &.active {
      color: white;
    }
  }
}

.navbar-center {
  @include center();
  gap: 8rpx;
  flex: 1;

  text {
    &:first-child {
      color: #1890FF;
    }
  }
}

.recommend {
  margin-top: 50rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  background: white;
  padding: 0 20rpx 30rpx 20rpx;
  position: relative;

  image {
    position: absolute;
    width: 152rpx;
    left: 0;
    top: -32rpx;
  }

  .header {
    padding: 32rpx 0 18rpx 100rpx;
    @include center();
    justify-content: space-between;


    view {
      &:first-child {
        font-weight: 600;
        background-image: url("https://rp.yjsoft.com.cn/yiban/static/reco-bg.png");
        background-size: cover;
        background-position: top center;

        text {
          color: #1890FF;
        }
      }

      .iconfont {
        color: black;
      }
    }
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}

.keywords {
  margin-top: 24rpx;
  padding: 0 $padding-page;

  .list {
    margin-top: 20rpx;
    display: flex;
    gap: 20rpx;
    font-size: 24rpx;
    flex-wrap: wrap;

    view {
      background: white;
      border-radius: 8rpx;
      padding: 8rpx 20rpx;
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
      @include center();
      justify-content: flex-start;
      gap: 5rpx;
    }
  }
}

.footer {
  width: 100%;
  display: flex;
  justify-content: center;
  // 确保footer内容在渐变背景上清晰可见
  z-index: 1;
  //position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  margin: 20rpx 0 0;
  padding: 0 0 34rpx;
}

.activity-content {
  image {
    width: 732rpx;
  }
}
