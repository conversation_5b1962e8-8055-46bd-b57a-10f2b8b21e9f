<script setup>

import {ref, watch} from "vue";

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  column: {
    type: Number,
    default: 2
  }
})

const columnHeight = ref(new Array(props.column).fill(0))
const columns = ref(new Array(props.column).fill(0).map(() => []))

function loadImageHeight(url) {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.src = url
    image.onload = () => {
      resolve(image.height)
    }
    image.onerror = () => {
      reject()
    }
  })
}

let nextIndex = 0

watch(props.data, async () => {
  while (nextIndex < props.data.length) {
    const item = props.data[nextIndex]
    let imageHeight = 0
    let minHeight = 0
    let minIndex = 0
    try {
      imageHeight = await loadImageHeight(item.image)
      minHeight = Math.min(...columnHeight.value)
      minIndex = columnHeight.value.findIndex(height => height === minHeight)
    } catch (e) {
      const lengths = columns.value.map((column) => {
        return column.length
      })
      const minLength = Math.min(...lengths)
      minIndex = lengths.findIndex(length => length === minLength)
    }

    columns.value[minIndex].push(item)
    columnHeight.value[minIndex] += imageHeight

    nextIndex++
  }
})

</script>

<template>
  <view class="yj-waterfall">
    <view v-for="(_, columnIndex) in columnHeight" :key="columnIndex" :class="`column-${columnIndex}`" class="column">
      <view v-for="(item, index) in columns[columnIndex]" :key="index">
        <slot :item="item"/>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>

.yj-waterfall {
  display: flex;
  flex-wrap: nowrap;

  .column {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

</style>