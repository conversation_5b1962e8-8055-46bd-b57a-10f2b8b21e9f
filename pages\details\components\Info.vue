<script setup>

import { computed, ref, watch } from "vue";
import { formatMetersToKilometers, formatMoney, showToast, navTo, getCdnUrl } from "@/utils";
import { planLike, planSubmit } from "@/api/plan";

const emit = defineEmits(['reload'])

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {
        plan_id: 0,
        sections: [],
        likes: 0,
        is_like: false,
        cost_ok: false,
      }
    }
  },
  showLikeAnimation: {
    type: Boolean,
    default: false
  }
})
const isLike = ref(props.detail.is_like)
const likes = ref(props.detail.likes || 0)
const days = computed(() => props.detail?.sections?.length || 0)
const sceneNum = computed(() => {
  let num = 0

  props.detail?.sections?.forEach((section) => {
    section.timeline.forEach((element) => {
      if (element.type === 'scene') {
        num++
      }
    })
  })

  return num
})

const planQueryParams = computed(() => {
  const p = {}

  if (props.detail.plan_id) {
    p.plan_id = props.detail.plan_id
  } else if (props.detail.ai_reqid) {
    p.ai_reqid = props.detail.ai_reqid
  }

  return p
})

watch(() => props.detail.likes, () => {
  likes.value = props.detail.likes || 0
  isLike.value = props.detail.is_like
})

async function onLike() {
  const data = { ai_reqid: props.detail.ai_reqid }
  if (props.detail.plan_id) {
    data.plan_id = props.detail.plan_id
  }

  planLike(data).then(({ data }) => {
    likes.value = data.likes
    isLike.value = data.op
    if (data.op) {
      showToast('点赞成功')
    } else {
      showToast('取消点赞成功')
    }
  })
}

</script>

<template>
  <view class="info">
    <view class="subject">{{ detail.subject }}</view>
    <view class="bg_view">
    </view>
    <view class="right_view" />
    <view class="summary">
      <view>
        <view>
          <image :src="getCdnUrl('/static/icons/calendar.png')" mode="widthFix" />
          <view>{{ days }}天</view>
        </view>
        <view>
          <image :src="getCdnUrl('/static/icons/scene.png')" mode="widthFix" />
          <view>{{ sceneNum }}个景点</view>
        </view>
        <view>
          <image :src="getCdnUrl('/static/icons/car.png')" mode="widthFix" />
          <view>{{ detail?.prompt_options?.transport || '自驾游' }}</view>
        </view>
      </view>
      <view style="margin-bottom: 10rpx">
        <view>
          <text class="iconfont icon-hangcheng1"></text>
          总行程：约{{ formatMetersToKilometers(detail.distance) }}公里
        </view>
        <view>
          <text class="iconfont icon-shijian"></text>
          最佳游玩：{{ detail.fit_for }}
        </view>
      </view>
      <view>
        <view class="cost-wrapper">
          <view>
            <text class="iconfont icon-renminbi"></text>
            人均：{{ formatMoney(detail.cost1) }}
          </view>
          <view :class="{ 'disabled': !detail.cost_ok }" class="expense-link"
            @tap="detail.cost_ok && navTo('pages/details/expense', planQueryParams)">
            <text class="iconfont icon-wenxintishi"></text>
            {{ detail.cost_ok ? '查看费用明细' : '费用明细计算中' }}
          </view>
        </view>
        <view class="like-container" @tap="onLike">
          <view class="like-button">
            <text :class="{
              liked: isLike,
              'like-bounce-animation': showLikeAnimation && !isLike
            }" class="iconfont icon-tuijian" />
            {{ likes }}点赞
          </view>
          <view v-if="showLikeAnimation && !isLike" class="like-tip">
            快来点赞吧！
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<style lang="scss" scoped>
@import '../../../styles/_define.scss';
@import '../../../styles/_mix.scss';

.info {
  position: relative;

  /* 创建定位上下文 */
  .bg_view {
    position: relative;
    /* 绝对定位 */
    //top: 70rpx;
    /* 调整位置，留出标题空间 */
    left: 0;
    right: 0;
    height: 186rpx;
    border-radius: 20rpx;
    /* 添加20rpx的圆角 */
    z-index: 1;
    /* 设置为最底层 */
    background: linear-gradient(to bottom, rgba(165, 234, 249, 0.7), rgba(238, 249, 233, 0.2)),
      linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 205, 25, .2), rgba(255, 250, 228, 0.2));
  }

  .right_view {
    background-image: url("https://rp.yjsoft.com.cn/yiban/static/home/<USER>/details/people-2.png");
    position: absolute;
    right: 0;
    top: -24rpx;
    /* 使底部与bg_view底部对齐(70rpx+186rpx-280rpx)，顶部超出bg_view */
    width: 196rpx;
    height: 280rpx;
    z-index: 2;
    /* 设置为中间层，高于bg_view */
    background-size: contain;
    background-repeat: no-repeat;
  }

  .subject {
    position: relative;
    /* 相对定位 */
    font-size: 34rpx;
    font-weight: 600;
    margin-bottom: 30rpx;
    z-index: 3;
    /* 设置为最顶层，与summary相同 */
    background: url("https://rp.yjsoft.com.cn/yiban/static/reco-bg.png") left top no-repeat;
    background-size: contain;
  }

  .liked {
    color: red;
  }

  .summary {
    position: absolute;
    top: 80rpx;
    /* 相对定位 */
    // margin-top: 10rpx; /* 为bg_view和right_view留出空间 */
    // margin-bottom: 20rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    height: 186rpx;
    font-size: 20rpx;
    z-index: 4;
    width: 100%;

    /* 设置为最顶层 */


    >view {
      &:first-child {
        font-size: 28rpx;
        margin-bottom: 20rpx;
      }

      &:last-child {
        justify-content: space-between;
      }

      @include center();
      justify-content: flex-start;
      gap: 10rpx;

      >view {
        @include center();
        gap: 8rpx;

        image {
          width: 40rpx;
        }
      }

      .cost-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8rpx;

        &,
        >view,
        .expense-link {
          @include center();
        }

        .expense-link {
          color: white;
          font-size: 20rpx;
          gap: 4rpx;
          padding: 0 6rpx;
          background: linear-gradient(180deg, #333333 0%, #626262 100%);
          border-radius: 30rpx;

          &.disabled {
            background: #cccccc;
            cursor: not-allowed;
            opacity: 0.7;
          }

          .iconfont {
            font-size: 18rpx;
          }
        }
      }
    }
  }

  // 点赞容器样式
  .like-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8rpx;
    animation: containerBreath 3s infinite ease-in-out;
    
    // 添加微妙的背景光晕
    &::before {
      content: '';
      position: absolute;
      top: -10rpx;
      left: -15rpx;
      right: -15rpx;
      bottom: -10rpx;
      background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
      border-radius: 50rpx;
      z-index: -1;
      animation: containerGlow 4s infinite ease-in-out;
    }
  }

  .like-button {
    display: flex;
    align-items: center;
    gap: 8rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    // 点赞文字增强
    font-weight: 500;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  }

  // 提示文案样式
  .like-tip {
    position: absolute;
    right: 120rpx;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 30%, #e84393 70%, #fd79a8 100%);
    color: white;
    font-size: 20rpx;
    font-weight: 600;
    padding: 10rpx 16rpx;
    border-radius: 25rpx;
    white-space: nowrap;
    animation: tipEnhanced 2.5s infinite cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow: 
      0 0 20rpx rgba(255, 107, 107, 0.6),
      0 8rpx 32rpx rgba(255, 71, 87, 0.4),
      inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3),
      inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid transparent;
    background-clip: padding-box;
    backdrop-filter: blur(10rpx);
    letter-spacing: 1rpx;
    
         // 渐变边框效果
     &::after {
       content: '';
       position: absolute;
       top: -2rpx;
       left: -2rpx;
       right: -2rpx;
       bottom: -2rpx;
       background: linear-gradient(45deg, #ff6b6b, #ff4757, #e84393, #fd79a8, #ff6b6b);
       background-size: 200% 200%;
       border-radius: 25rpx;
       z-index: -1;
       animation: borderGlow 3s infinite linear;
     }
    
    // 气泡箭头
    &::before {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 10rpx 0 10rpx 12rpx;
      border-color: transparent transparent transparent #ff6b6b;
      filter: drop-shadow(2rpx 2rpx 4rpx rgba(255, 107, 107, 0.3));
    }
  }

  @keyframes tipEnhanced {
    0%, 100% {
      opacity: 0.9;
      transform: translateX(0) scale(1) rotate(0deg);
    }
    25% {
      opacity: 1;
      transform: translateX(8rpx) scale(1.05) rotate(1deg);
    }
    50% {
      opacity: 1;
      transform: translateX(12rpx) scale(1.08) rotate(-0.5deg);
    }
    75% {
      opacity: 1;
      transform: translateX(6rpx) scale(1.02) rotate(0.5deg);
    }
  }

  @keyframes borderGlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes containerBreath {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }

  @keyframes containerGlow {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  // 点赞按钮增强动画
  .like-bounce-animation {
    animation: likeEnhanced 1.8s infinite cubic-bezier(0.68, -0.55, 0.265, 1.55);
    filter: drop-shadow(0 0 8rpx rgba(255, 107, 107, 0.5));
  }
  
  @keyframes likeEnhanced {
    0%, 100% {
      transform: scale(1) rotate(0deg);
      color: inherit;
      text-shadow: none;
    }
    15% {
      transform: scale(1.3) rotate(-5deg);
      color: #ff6b6b;
      text-shadow: 0 0 20rpx rgba(255, 107, 107, 0.8);
    }
    30% {
      transform: scale(0.9) rotate(3deg);
      color: #ff4757;
      text-shadow: 0 0 15rpx rgba(255, 71, 87, 0.6);
    }
    50% {
      transform: scale(1.25) rotate(-2deg);
      color: #e84393;
      text-shadow: 0 0 25rpx rgba(232, 67, 147, 0.9);
    }
    70% {
      transform: scale(1.1) rotate(1deg);
      color: #fd79a8;
      text-shadow: 0 0 18rpx rgba(253, 121, 168, 0.7);
    }
    85% {
      transform: scale(1.05) rotate(-1deg);
      color: #ff6b6b;
      text-shadow: 0 0 12rpx rgba(255, 107, 107, 0.5);
    }
  }

}
</style>
