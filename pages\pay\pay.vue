<template>
  <view class="container">
    <!-- 订单信息 -->
    <view class="order-info">
      <!-- 支付剩余时间 -->
      <!-- <view v-if="expireTime" class="order-countdown">
        <text class="m-r-6">剩余时间</text>
        <count-down :date="expireTime" separator="zh" theme="text" @finish="onCountEnd" />
      </view> -->
      <!-- 付款金额 -->
      <!-- <view class="order-amount">
        <text class="unit">￥</text>
        <text class="amount">{{ formatMoney(payAmount, 2, false) }}</text>
      </view> -->
    </view>

    <!-- 支付方式 -->
    <view class="payment-method">
      <view v-for="(item, index) in methods" :key="index" class="pay-item dis-flex flex-x-between"
            @click="doSelectMethod(item)">
        <view class="item-left dis-flex flex-y-center">
          <view :class="[item.method]" class="item-left_icon">
            <text :class="['iconfont', PayMethodIconEnum[item.method]]"></text>
          </view>
          <view class="item-left_text">
            <text>{{ item.name }}</text>
          </view>
        </view>
        <view v-if="curMethod == item.value" class="item-right col-m">
          <text class="iconfont icon-duigoux"></text>
        </view>
      </view>
    </view>

    <!-- 确认按钮 -->
    <view class="footer-fixed">
      <view class="btn-wrapper">
        <view :class="{ disabled }" class="btn-item btn-item-main" @click="doSubmit">确认支付</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {
  onLoad
} from '@dcloudio/uni-app'
import {
  computed,
  ref
} from 'vue';
import {
  orderPay,
  orderPayConfirm
} from '../../api/order';
import {
  navBack,
  navTo,
  showError,
  showToast
} from '../../utils';
import {
  doPay
} from '../../utils/pay';
import {
  PayMethodEnum
} from '@/utils/enum/pay';
import {
  uniGetProvider,
  uniPay
} from '@/utils/uni';
import {packagePay} from "@/api/point";
import {PayTypeOrder} from "@/utils/constmap";

const PayMethodIconEnum = {
  [PayMethodEnum.WechatH5.method]: 'icon-weixinzhifu',
  [PayMethodEnum.WechatMp.method]: 'icon-weixinzhifu',
}
const orderId = ref('')
const payId = ref('')
const methods = ref([])
const curMethod = ref(0)
const disabled = computed(() => {
  return !curMethod.value
})
const payType = ref(10)
const packageId = ref(0)

// #ifdef H5
methods.value.push(PayMethodEnum.WechatH5)
// #endif
// #ifdef MP-WEIXIN
methods.value.push(PayMethodEnum.WechatMp)
// #endif


const doSelectMethod = (payMethod) => {
  if (payMethod && payMethod.value) {
    curMethod.value = payMethod.value
  }
}

const doSubmit = async () => {
  if (disabled.value) {
    return
  }
  try {
    const requests = [uniGetProvider('payment')]
    if (payType.value === PayTypeOrder) {
      requests.unshift(orderPay({
        order_id: orderId.value,
        method: curMethod.value,
      }))
    } else {
      requests.unshift(packagePay({package_id: packageId.value}))
    }

    let [res, pres] = await Promise.all(requests)

    let params = {
      provider: pres.provider[0],
    }

    payId.value = res.data.pay_id
    switch (curMethod.value) {
      case PayMethodEnum.WechatH5.value:
      case PayMethodEnum.WechatMp.value:
        const {
          pay_str
        } = res.data
        const paymentData = JSON.parse(pay_str)
        params = {
          ...params,
          ...paymentData,
        }
        break
    }

    await uniPay(params)
    await orderPayConfirm({
      pay_id: payId.value,
    })
    await showToast('支付成功', 'success')

    if (payType.value === PayTypeOrder) {
      navTo('pages/orders/orderdetail', {
        id: orderId.value,
      }, true)
    } else {
      navBack(2)
    }
  } catch (e) {
    console.error('=====pay error', e)
    await showError(e && (e.message || e.msg) || '支付失败')

    if (payType.value === PayTypeOrder) {
      navTo('pages/orders/orders', {}, true)
    } else {
      navBack(2)
    }
  }
}

onLoad(query => {
  orderId.value = query.order_id
  payType.value = query.pay_type || PayTypeOrder
  packageId.value = query.package_id

  doSelectMethod(methods.value[0])
  if (curMethod.value) {
    doSubmit()
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/_define.scss';
@import '@/styles/_mix.scss';

.container {
  background: $page-bg-color;
  min-height: 100vh;
}

// 订单信息
.order-info {
  padding: 80rpx 0;
  text-align: center;

  .order-countdown {
    display: flex;
    justify-content: center;
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 20rpx;
  }

  .order-amount {
    margin: 0 auto;
    max-width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #111111;

    .unit {
      font-size: 30rpx;
      margin-bottom: -18rpx;
    }

    .amount {
      font-size: 56rpx;
    }
  }
}

// 支付方式
.payment-method {
  width: 94%;
  margin: 0 auto 20rpx auto;
  padding: 0 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;

  .pay-item {
    padding: 26rpx 0;
    font-size: 28rpx;
    border-bottom: 1rpx solid rgb(248, 248, 248);
    @include center();
    justify-content: space-between;

    .item-left {
      @include center();
    }

    &:last-child {
      border-bottom: none;
    }

    .item-left_icon {
      margin-right: 20rpx;

      .iconfont {
        font-size: 44rpx;
      }


      &.wechat {
        color: #00c800;
      }

      &.alipay {
        color: #009fe8;
      }

      &.balance {
        color: #ff9700;
      }
    }

    .item-left_text {
      font-size: 28rpx;
    }

    .item-right {
      font-size: 32rpx;
    }

    .user-balance {
      margin-left: 20rpx;
      font-size: 26rpx;
    }
  }

}

// 底部操作栏
.footer-fixed {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;

  // 设置ios刘海屏底部横线安全区域
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .btn-wrapper {
    height: 120rpx;
    display: flex;
    align-items: center;
    padding: 0 40rpx;
  }

  .btn-item {
    flex: 1;
    font-size: 28rpx;
    height: 80rpx;
    color: #fff;
    border-radius: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .btn-item-main {
    background: $plan-color-1;
    color: white;

    // 禁用按钮
    &.disabled {
      opacity: 0.6;
    }
  }

}
</style>
