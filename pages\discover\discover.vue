<template>
  <view class="container">
    <swiper :autoplay="true" :duration="1000" :indicator-dots="false" :interval="3000">
      <swiper-item v-for="(item, index) in banners" :key="index">
        <image :src="item.pic" class="image" mode="widthFix" @tap="doBannerTap(item)"/>
      </swiper-item>
    </swiper>
    <YjTabs v-model="cur" :data="tabs"/>

    <YjWaterfall :data="list">
      <template v-slot="{ item: { item } }">
        <view class="tuan-item" @tap="onNavTo(item)">
          <image :src="item.pic" class="image" mode="widthFix"/>
          <view>
            <view class="name">{{ item.name }}</view>
            <template v-if="item.type !== 'activity'">
              <view>
                <view class="tag hot">火热</view>
                <view class="tag">小拼团</view>
              </view>
              <view class="price">{{ formatMoney(item.price) }}
                <text>起</text>
              </view>
            </template>

          </view>
        </view>
      </template>
    </YjWaterfall>

    <uni-load-more :status="status"/>

  </view>
</template>

<script setup>
import {onLoad, onReachBottom} from '@dcloudio/uni-app'
import {computed, ref} from 'vue';
import {tuans} from '../../api/tuan';
import YjTabs from '@/components/YjTabs/YjTabs.vue';
import YjWaterfall from "@/components/YjWaterfall/YjWaterfall.vue";
import UniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
import {formatMoney, navTo} from "../../utils";
import {finder} from "@/api/finder";

const cur = ref('all')
const tabs = ref([])
const banners = ref([])
const list = ref([])
const total = ref(0)
const pageSize = 20
let page = ref(1)
const totalPage = computed(() => Math.ceil(total.value / pageSize))
const loading = ref(false)
const status = computed(() => {
  if (loading.value) {
    return 'loading'
  }
  return totalPage.value > page.value ? 'more' : 'noMore'
})
const days = ref([])

function getList() {
  if (loading.value) {
    return
  }

  const params = {
    page: page.value,
    page_size: pageSize
  }

  loading.value = true

  tuans(params).then(res => {
    const {
      data
    } = res

    data.list.forEach(item => {
      list.value.push({
        image: item.pic,
        item
      })
    })
    total.value = data.total
  }).finally(() => {
    loading.value = false
  })
}

const doBannerTap = (item) => {
  item.link && navTo(item.link)
}

onLoad(() => {
  navTo('pages/index/index')
  getList()

  finder().then(({data}) => {
    banners.value = data.banners
    tabs.value = data.tabs
  })
})

function onNavTo(item) {
  if (item.type === 'activity') {
    navTo(item.link)
  } else {
    navTo('pages/tuan/detail', {id: item.id})
  }
}

onReachBottom(() => {
  if (totalPage.value > page.value) {
    page.value += 1
    getList()
  }
})
</script>

<style lang="scss" scoped>
@import 'discover';
</style>