<template>
  <view class="travel">
    <view class="header">
      <view class="from-to">
        <text class="iconfont icon-ditu"/>
        <text>{{ truncateText(travel.prompt_options.from) }}</text>
        <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/icons/fly-to.png"></image>
        <text>{{ truncateText(travel.prompt_options.to) }}</text>
      </view>
      <view class="summary">
        <view>
          <text>{{ travel.days }}天</text>
          <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/icons/calendar.png"></image>
        </view>
        <view>
          <text>{{ travel.scene_num }}个</text>
          <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/icons/scene.png"></image>
        </view>
        <view>
          <text>{{ truncateText(travel.prompt_options.transport || '自驾') }}</text>
          <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/icons/car.png"></image>
        </view>
      </view>
    </view>

    <view v-if="!isLock" class="content">
      <view v-if="travel.integral_cost" class="integral-cost">
        <text>-{{ travel.integral_cost }}</text>
        <image src="https://rp.yjsoft.com.cn/yiban/static/points/points_selected.png"></image>
      </view>
      <view class="list">
        <view v-for="(section, sk) in travel.sections" :key="'section-' + sk" class="list-item">
          <view class="subject">{{ section.section_name }}</view>
          <view class="extra">
            {{
              section.timeline
                  .filter((item) => item.type == 'scene')
                  .map((item) => item.title)
                  .join('•')
            }}
          </view>
          <view class="tags">
            <text v-for="(tag, tagIndex) in section.tags" :key="'tag-' + sk + '-' + tagIndex">{{ tag }}</text>
          </view>
        </view>
      </view>
      <view class="buttons">
        <view class="left">
          <YjTooltip v-if="isLast" :conflict="conflictTips" content="重新规划" placement="top" type="index-retry">
            <text class="iconfont icon-shuaxin1" @tap="onRetry(travel)"></text>
          </YjTooltip>
          <text v-else class="iconfont icon-shuaxin1" @tap="onRetry(travel)"></text>
          <text :class="{ active: travel.is_own }" class="iconfont icon-shoucang3 fav" @tap="onFav(travel)"></text>


        </view>
        <view class="right" @tap="onDetail(travel)">
          查看详情
          <text class="iconfont icon-gengduo"/>
        </view>
      </view>
    </view>
    <view v-else class="content image-content" @tap="onImageClick(travel)">
      <image class="plan-image" mode="aspectFill"
             src="https://rp.yjsoft.com.cn/yiban/static/home/<USER>/plan-card-lock.png"></image>
    </view>

    <view v-if="showReplace && !isLock" class="replace-container">
      <MyButton custom-class="replan-btn" icon="icon-qiehuan1" size="small" type="success"
                @tap="emit('replace', travel.ai_reqid)">更换
      </MyButton>
    </view>

    <MyActivityDoTaskPopup :data="doTaskData"></MyActivityDoTaskPopup>
  </view>
</template>

<script setup>

import {planDetail, planSubmit} from '@/api/plan';
import {planRemove} from '@/api/user';
import {navTo, showToast} from '@/utils';
import {conflictTips} from '@/utils/constmap';
import {onMounted, ref, watch} from 'vue'
import {trackEvent} from "@/utils/tracker";
import MyActivityDoTaskPopup from "@/components/MyActivityDoTaskPopup/MyActivityDoTaskPopup.vue";
import {TaskCondTypeSavePlan} from "@/utils/constmap_activity";
import YjTooltip from "@/components/YjTooltip/YjTooltip.vue";
import MyButton from "@/components/MyButton/MyButton.vue";

const emit = defineEmits(['retry', 'remove', 'load', 'imageClick', 'replace'])

const props = defineProps({
  data: {
    type: Object,
    default: () => {
    }
  },
  requestId: {
    type: String,
    default: ''
  },
  isLast: {
    type: Boolean,
    default: false
  },
  needDeduct: {
    type: Number,
    default: 1
  },
  showReplace: {
    type: Boolean,
    default: false
  },
  unlocked: {
    type: Boolean,
    default: true
  }
})

const travel = ref({
  prompt_options: {
    transport: '自驾'
  },
  sections: [],
})

const doTaskData = ref({})

const isLock = ref(false)

// 文字截取方法，限制最长4个字
function truncateText(text, maxLength = 4) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function onDetail(item) {
  navTo('pages/details/details', {
    ai_reqid: item.ai_reqid
  })
}

function onRetry(item) {
  trackEvent('click_retry_plan')
  emit('retry', {
    travel_data: item,
    request_id: props.request_id,
  })
}

function onFav(item) {
  const params = item.plan_id ? {plan_id: item.plan_id} : {ai_reqid: item.ai_reqid}

  if (!item.is_own) {
    trackEvent('click_fav_plan')
    planSubmit(params).then(({data}) => {
      showToast('收藏成功')
      item.is_own = true
      item.plan_id = data.id
      doTaskData.value = {
        cond: TaskCondTypeSavePlan,
        plan_id: data.id,
      }
    })
  } else {
    planRemove(params.plan_id).then(() => {
      showToast('取消收藏成功')
      item.is_own = false
    })
  }
}

function onImageClick(item) {
  emit('imageClick', {
    travel_data: item,
    request_id: props.request_id,
  })
}

async function getDetail() {
  if (props.requestId.length > 0) {
    try {
      const {data} = await planDetail({ai_reqid: props.requestId, need_deduct: props.needDeduct})

      const processedData = JSON.parse(JSON.stringify(data))

      if (processedData.sections?.length) {
        processedData.sections = processedData.sections.slice(0, 3).map(section => {
          const tags = new Set()
          section.timeline?.forEach(item => item.tags?.forEach(tag => tags.add(tag)))
          return {
            ...section,
            tags: Array.from(tags).slice(0, 3)
          }
        })
      }

      // 将处理好的数据赋值给travel.value
      Object.assign(travel.value, processedData)
      if (travel.value.integral_cost_state == 2) {
        showToast('积分不足')
        isLock.value = true
      } else {
        isLock.value = false
      }
      emit('load', {request_id: props.requestId})
    } catch (e) {
      console.log(e)
      if (e.code == 900021) {
        showToast('积分不足')
        isLock.value = true
        return
      }
    }
  } else {
    Object.assign(travel.value, props.data)
  }
}

// 监听 unlocked 属性的变化，重新获取详情数据
watch(() => props.unlocked, (newVal, oldVal) => {
  // 当 unlocked 发生变化时，重新获取详情
  // 避免初始化时的重复调用（onMounted 已经调用了 getDetail）
  if (oldVal !== undefined && newVal !== oldVal) {
    getDetail()
  }
}, {immediate: true})

onMounted(() => {
  getDetail()
})

</script>

<style lang="scss" scoped>
@import "myplancard";
</style>
