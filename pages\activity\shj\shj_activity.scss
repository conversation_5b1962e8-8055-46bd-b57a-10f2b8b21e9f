@import "../../../styles/_define.scss";
@import "../../../styles/_mix.scss";

// 隐藏滚动条的混合样式
@mixin hide-scrollbar {
  // 隐藏滚动条 - WebKit浏览器（Chrome、Safari、新版Edge等）
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  // 隐藏滚动条 - Firefox
  scrollbar-width: none;

  // 隐藏滚动条 - IE和Edge
  -ms-overflow-style: none;

  // 防止滚动条在滚动过程中出现
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  @include hide-scrollbar;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.background-area {
  width: 100%;
  min-height: 100vh; /*  确保至少占满整个视口高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* 添加相对定位 */
}

.top-image,
.bottom-image {
  width: 100%;
  display: block;
}

/* 中间渐变背景区域 */
.gradient-area {
  width: 100%;
  flex-grow: 1; /* 填充顶部和底部图片之间的空间 */
  background: linear-gradient(to bottom, #1D352F, #18322E);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

/* 渐变区域文本样式 */
.gradient-text {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  margin-bottom: 20rpx;
}

/* 新增的视图层 */
.overlay-layer {
  position: absolute;
  width: 100%;
  z-index: 10; /* 确保覆盖在渐变背景和底部图片上方 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 500rpx; /* 调整上边距，使其不完全覆盖渐变区域 */
}

/* 卡片容器样式 */
.cards-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0, 30rpx; /* 添加左右内边距 */
  z-index: 20; /* 确保卡片在新视图层上方 */

  /* 禁用点击高亮效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* 卡片行样式 */
.cards-row {
  width: 100%;
  height: 400rpx; /* 设置足够的高度容纳卡片 */
  display: flex;
  flex-direction: row;
  justify-content: center; /* 居中对齐卡片 */
  align-items: center;
  gap: 50rpx; /* 设置卡片之间的间距为50rpx */
}

/* 卡片包装器样式 */
.card-wrapper {
  display: flex;
  flex-direction: column; /* 修改为纵向排列，使箭头可以在卡片下方 */
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  transform-origin: center center;
  cursor: pointer; /* 添加手型样式，提示可点击 */
  position: relative; /* 添加相对定位，作为箭头的定位参考 */

  /* 禁用点击高亮效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  outline: none;

  &.active {
    transform: scale(1.05); /* 选中时的放大效果 */
    filter: brightness(1.1); /* 增加亮度 */
    z-index: 10; /* 确保选中的卡片在最上层 */
  }
}


/* 箭头指示器样式 */
.arrow-indicator {
  width: 40rpx;
  height: 20rpx;
  margin-top: 10rpx; /* 距离卡片顶部10rpx */
  position: absolute;
  bottom: -30rpx; /* 定位在卡片下方 */
  left: 50%;
  transform: translateX(-50%); /* 水平居中 */
  z-index: 11; /* 确保箭头在卡片上层 */
}

/* 卡片描述区域样式 */
.card-description-area {
  width: 100%;
  padding: 20rpx 216rpx 30rpx 57rpx; /* 左右间距分别为57rpx和15rpx */
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 左对齐而不是居中 */
  z-index: 20;
  color: #ffffff;
  text-align: left; /* 文本左对齐 */
}

/* 卡片标题样式 */
.card-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 卡片描述样式 */
.card-description {
  font-size: 34rpx;
  line-height: 1.5;
  margin-bottom: 30rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  @include ellipse(5);
}

/* 卡片操作按钮区域 */
.card-actions {
  display: flex;
  justify-content: flex-start; /* 左对齐而不是居中 */
  gap: 40rpx;
  margin-top: 20rpx;
  background-color: rgba(0, 0, 0, 0.6); /* 60%透明度的黑色背景 */
  border-radius: 30rpx; /* 圆角为30rpx */
  padding: 9rpx 40rpx; /* 内间距为左右40rpx，上下9rpx */

  /* 直接在.card-actions中定义text样式，增加特异性 */
  .action-button text {
    font-size: 28rpx !important;
    color: #ffffff !important;
    line-height: 28rpx !important;
  }
}

.action-text {
  font-size: 34rpx !important;
  color: #ffffff;
}

.action-text_margin {
  margin-top: 20rpx;
  font-size: 34rpx !important;
  color: #ffffff;
}

/* 操作按钮样式 */
.action-button {
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  align-items: center;
  gap: 10rpx; /* 图标和文字之间的间距 */
  .action-icon {
    width: 40rpx;
    height: 40rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  text {
    font-size: 28rpx !important;
    color: #ffffff;
    line-height: 28rpx !important;
  }
}

/* 卡片正面样式 */
.card-front {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 卡片背面样式 */
.card-back {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 卡片背景图片样式 */
.card-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 卡片数字图片样式 */
.card-num {
  width: 140rpx;
  height: 224rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5; /* 增加z-index确保显示在背景之上 */
}

/* 卡片内容图片样式 */
.card-content {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16rpx; /* 添加圆角 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.fbtn {
  width: 132rpx;
  height: 132rpx;
  background: #E2624A;
  border: 6rpx solid #F6E685;
  border-radius: 50%;
  font-weight: 500;
  font-size: 34rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  text-align: center;
  @include center();
}

.bottom-right-view-wrap {
  position: fixed;
  bottom: 147rpx; /* 将宝箱向上移动 */
  right: 0;
  z-index: 70;
}

.bottom-right-view {
  width: 213rpx;
  height: 213rpx;


  background-image: url("https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/bottom_right_bg.png");
  background-position: bottom right;
  background-size: 213rpx 213rpx;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;


  /* 宝箱图片样式 */
  .treasure-box {
    width: 180rpx;
    height: 180rpx;
    position: absolute;
    right: 0; /* 靠右对齐 */
    top: 50%; /* 垂直方向上的中心点 */
    transform: translateY(-70%); /* 垂直居中并向上偏移 */
    z-index: 101;
  }

  /* 状态描述样式 */
  .status-description {
    position: absolute;
    top: 50%; /* 将状态描述放在宝箱的垂直中心点 */
    right: 50%; /* 将状态描述放在宝箱的水平中心点 */
    transform: translate(50%, -80%); /* 完全居中并向上偏移 */
    width: auto; /* 根据内容自动调整宽度 */
    display: flex;
    justify-content: center;
    z-index: 102;

    .status-text {
      padding: 5rpx 20rpx;
      background-color: rgba(0, 0, 0, 0.6);
      color: #f6e685;
      font-size: 24rpx;
      border-radius: 20rpx;
      font-weight: bold;
      text-align: center;
      white-space: nowrap; /* 防止文字换行 */
    }

    .claim-text {
      color: #f6e685; /* 金色文字，更醒目 */
      font-weight: 500;
      animation: pulse 0.8s infinite alternate; /* 添加脉冲动画 */
    }

    .claimed-text {
      color: #f6e685;
      font-weight: 500;
    }
  }

  /* 宝箱底部描述样式 */
  .bottom-description {
    position: absolute;
    bottom: 10rpx; /* 将底部描述放在宝箱下方，但仍在视图内部 */
    right: 0; /* 靠右对齐 */
    width: 176rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 102;

    .bottom-text {
      padding: 10rpx 10rpx;
      color: #ffffff;
      font-size: 20rpx;
    }
  }

  /* 当状态为可领取时添加脉冲动画 */
  &.can-claim {
    .treasure-box {
      transform-origin: center; /* 确保缩放以中心为基准 */
      animation: pulse 0.8s infinite alternate;
    }

    /* 确保状态描述在可领取状态下的位置正确 */
    .status-description {
      transform: translate(50%, -70%); /* 保持与宝箱一致的位移 */
    }
  }

  /* 当状态为已领取时调整宝箱位置 */
  &.claimed {
    .treasure-box {
      transform: translateY(
                      -60%
      ); /* 在已领取状态下将宝箱向下移动，因为没有底部描述文字 */
    }

    /* 确保状态描述在已领取状态下的位置正确 */
    .status-description {
      transform: translate(50%, -50%); /* 保持与宝箱一致的位移 */
    }
  }
}

/* 脉冲动画效果 */
@keyframes pulse {
  from {
    transform: translateY(-70%) scale(1);
  }
  to {
    transform: translateY(-70%) scale(1.05);
  }
}

/* 打卡成功动画遮罩层样式 */
.success-animation-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease forwards;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 成功动画容器 */
.success-animation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 40rpx;
}

/* 成功提示文字 */
.success-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.5);
  animation: scaleIn 0.5s ease forwards;
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 成功卡片包装器 */
.success-card-wrapper {
  animation: cardAppear 0.5s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: scale(0.8);
}

/* 卡片出现动画 */
@keyframes cardAppear {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 添加关闭按钮样式 */
.close-button {
  margin-top: 40rpx;
  padding: 20rpx 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid #ffffff;
  border-radius: 40rpx;
  animation: fadeIn 1s ease forwards;
  animation-delay: 1s;
  opacity: 0;
  
  text {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
  }
  
  &:active {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

.select-delivery {
  padding: 23rpx 37rpx;
  min-height: 757rpx;
  display: flex;
  flex-direction: column;

  .header {
    @include center();
    justify-content: space-between;
    margin-bottom: 20rpx;

    text {
      font-weight: bold;
    }
  }

  .delivery-footer {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding-bottom: 20rpx;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .address-name {
      color: #1890FF;
      margin-bottom: 10rpx;
    }

    :deep(.uni-forms-item__label) {
      font-weight: bold;
      color: $black-color;
    }

    :deep(.uni-forms-item__content) {
      @include center(column);
      align-items: flex-start;
    }

    :deep(.input-value ) {
      padding-left: 0;
    }

    :deep(.uni-forms-item__error) {
      position: inherit;
      top: auto;
      left: auto;
    }

    :deep(.my-input) {
      background: transparent;
      border-radius: 0;
      padding: 0;
    }

    :deep(.uni-forms-item) {
      margin-bottom: 0;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #EDEEF0;
    }
  }
}