const globalEvent = new class {
  evtMap = {}
  register(evt, cb) {
    if (evt in this.evtMap) {
      this.evtMap[evt].push(cb)
    } else {
      this.evtMap[evt] = [cb]
    }
  }
  unregister(evt, cb) {
    if (evt in this.evtMap) {
      let idx = this.evtMap[evt].findIndex(v => v === cb)
      if (idx > -1) {
        this.evtMap[evt].splice(idx, 1)
      }
    }
  }
  trigger(evt, data) {
    if (evt in this.evtMap) {
      this.evtMap[evt].forEach(v => v(data))
    }
  }
}


export const GlobalEvent = globalEvent