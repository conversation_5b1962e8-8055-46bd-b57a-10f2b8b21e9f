@import "../../../styles/_define.scss";
@import "../../../styles/_mix.scss";

.refund-order-list {
  min-height: 100vh;
  background: $page-bg-color;

  .list {
    padding: $padding-page;
    display: flex;
    flex-direction: column;
    gap: $padding-v2;

    .order-item {
      box-shadow: $box-shadow;
      background: white;
      border-radius: 20rpx;
      padding: 30rpx; // 从 $padding-page 修改为 30rpx
      border: 1rpx solid $border-color-v2;

      .header {
        &,
        .left,
        .right {
          @include center();
          justify-content: space-between;
        }

        // 添加通用文字样式
        font-size: 24rpx;
        color: $black-color;

        .left {
          gap: $padding-small;

          .iconfont {
            width: 40rpx;
            height: 40rpx;
            background: $plan-color-3;
            color: white;
            display: inline-block;
            border-radius: 50%;
            font-size: 24rpx;
            @include center();

            &.icon-jingdian {
              background: $plan-color-2;
            }

            &.icon-huowudui {
              background: #faca14;
            }

            &.icon-chuang {
              background: $plan-color-4;
            }
          }
        }

        .right {
          gap: 8rpx;

          text {
            font-size: 24rpx; // 明确设置右侧文字大小

            &:first-child {
              padding-right: 8rpx;
              border-right: 2rpx solid $border-color;
              color: $black-color-v3;
            }

            // 添加第二个文字（状态）的样式
            &:last-child {
              color: $plan-color-1;
            }
          }
        }
      }

      .details {
        // margin: $padding-v2 0;

        .detail {
          display: flex;
          justify-content: flex-start;
          gap: $padding-small;
          padding-bottom: $padding-v2;
          padding-top: $padding-v2;

          & {
            border-bottom: 2rpx solid #edeef0; // 从 1rpx solid $border-color-v2 修改为 2rpx solid #EDEEF0
          }

          .left {
            width: 160rpx;
            height: 160rpx;
            border-radius: $border-radius-v2;
          }

          .right {
            margin-left: 10rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;

            > view {
              &:first-child {
                @include center(column);
                align-items: flex-start;
                gap: $padding-mini;
                font-size: $fontsize-small;
                color: $font-color-gray;

                .name {
                  max-width: 450rpx;
                  font-weight: 500;
                  @include ellipse();
                  font-size: $fontsize;
                  color: #333;
                }

                .sku_name {
                  max-width: 480rpx;
                  @include ellipse();
                  font-weight: 400; // 添加字重设置
                  font-size: 24rpx;
                }

                .date-info {
                  font-size: $fontsize-small;
                  color: $font-color-gray;
                  font-weight: 400; // 添加字重设置

                  .hotel-date {
                    .date-range {
                      display: flex;
                      align-items: flex-start;

                      .date-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        .weekday {
                          font-size: 20rpx;
                          margin-bottom: 4rpx;
                        }
                      }

                      .separator {
                        margin: 0 8rpx;
                        align-self: flex-end;
                        padding-bottom: 2rpx; // 微调位置，使其与日期对齐
                      }
                    }
                  }
                }
              }

              &:last-child {
                text-align: right;
                font-weight: 600;
              }
            }
          }
        }

        .more-items {
          text-align: center;
          padding: $padding-small 0;
          color: $font-color-gray;
          font-size: $fontsize-small;
        }
      }

      .title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin-top: $padding-v2;
      }

      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
          color: $font-color-gray;
          font-size: $fontsize-small;

          .price-info {
            .total-price {
              margin-top: 20rpx;
              font-weight: 400;
              font-size: 28rpx;
              color: $black_color;
            }

            .refund-amount {
              margin-top: 10rpx;
              font-weight: 400;
              font-size: 28rpx;
              color: $plan-color-3;
            }
          }
        }

        .right {
          display: flex;
          justify-content: flex-end;

          .btn {
            background: white;
            margin-left: $padding-small;
          }

          .detail-btn {
            margin-top: 36rpx;
            color: $plan-color-3;
            border: 1rpx solid $plan-color-3;
            background: #e7f4ff;
            border-radius: 36rpx; // 添加圆角
            padding: 16rpx 44rpx; // 设置内部上下左右间距
            font-size: 28rpx; // 设置字号
            font-weight: 500; // 设置字重
          }
        }
      }
    }
  }
}
