<template>
  <view class="container">
    <view class="header">
      <view>
        <MyInput v-model="search.keyword" before-icon="icon-sousuo" class="input" placeholder="搜索目的地/景点"/>
      </view>

      <view class="zones">
        <scroll-view :scroll-y="false" scroll-x enhanced :show-scrollbar="false">
          <view class="zone-list">
            <view :class="{
              active: !search.zone_ids
            }" @tap="search.zone_ids = ''">全部
            </view>
            <view v-for="(item, index) in zones" :key="index" :class="{
              active: search.zone_ids === item.id
            }" @tap="search.zone_ids = item.id">{{ item.name }}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <view class="list">
      <view v-for="item in list" :key="item.id" class="list-item">
        <view class="pic">
          <image :src="item.pic" mode="aspectFill"/>
        </view>
        <view class="right">
          <view>
            <view class="name" @tap="navTo('pages/scenic/scenic', { id: item.id })">{{ item.name }}
              <text class="iconfont icon-gengduo"></text>
            </view>
            <view>
              <view v-if="item.level > 0" class="tag">{{ item.level }}A</view>
              <view v-if="joinSelectPoiText(item)" class="tag join">{{ joinSelectPoiText(item) }}</view>
            </view>
            <view class="location">{{ item.city_name }} | 距离市中心{{ item.zone_distance.toFixed(2) }}公里</view>
          </view>
          <view>
            <label v-if="type == 'add'">
              <checkbox :checked="isIncludes(item)" :value="item.id" active-background-color="#1890FF"
                        border-color="#1890FF" icon-color="#fff" @tap="onListItemTap(item)"></checkbox>
            </label>
            <MyButton v-else-if="type != 'add' && objId != item.id" link type="primary" @tap="onListItemTap(item)">替换
            </MyButton>

          </view>

        </view>
      </view>
    </view>

    <uni-load-more :status="loadMoreStatus"/>

    <view class="footer">
      <view class="selected">
        <view v-for="(item, index) in selectIds" :key="index" class="tag cancel">
          {{ item.name }}
          <text class="iconfont icon-guanbi" @tap="onSelectPoi(item.id, selectDay, item.name)"/>
        </view>
      </view>
      <view class="actions">
        <view class="left">
          添加至：
          <uni-data-select v-model="selectDay" :clear="false" :localdata="days" placement="top"/>
        </view>
        <MyButton v-if="type == 'add'" type="primary" @tap="onSubmit">添加</MyButton>
      </view>
    </view>

  </view>
</template>

<script setup>

import MyInput from "@/components/MyInput/MyInput.vue";
import {onLoad, onReachBottom} from "@dcloudio/uni-app";
import {useGlobalStore} from "@/store/global";
import {computed, nextTick, ref, watch} from "vue";
import {formatMoney, hideLoading, navBack, navTo, showLoading} from "@/utils";
import MyButton from "@/components/MyButton/MyButton.vue";
import UniDataSelect from "@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue";
import {sceneSearch, scenicDetail} from "@/api/scene";
import UniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";

const globalStore = useGlobalStore()
const zones = computed(() => {
  const list = []
  if (globalStore.editPlan) {
    globalStore.editPlan.sections.forEach(item => {
      if (!item.zones || item.zones.length === 0) {
        return
      }
      for (const zone of item.zones) {
        const index = list.findIndex(i => i.id === zone.id)
        if (index === -1) {
          list.push(zone)
        }
      }
    })
  }

  return list
})
const list = ref([])
const total = ref(0)
const selectDay = ref(0)
const days = computed(() => {
  return globalStore.editPlan.sections.map((item, index) => {
    return {
      text: `第${index + 1}天`,
      value: index,
    }
  })
})
const search = ref({
  keyword: '',
  zone_ids: '',
})
const page_size = 10
const isLoading = ref(false)
const page = ref(1)
const hasMore = computed(() => page.value < Math.ceil(total.value / page_size))
const loadMoreStatus = computed(() => {
  if (isLoading.value) {
    return 'loading'
  }
  return hasMore.value ? 'more' : 'noMore'
})
const selectIds = ref({})
const type = ref('')
const objId = ref('')
const isSelecting = ref(false)

watch([() => search.value.keyword, () => search.value.zone_ids], () => {
  nextTick(() => {
    list.value = []
    page.value = 1
    getList()
  })
})

async function packItem(id) {
  const {data} = await scenicDetail(id, false)
  return {
    is_free: data.is_free,
    scene: data.scene,
    avg_price: data.avg_price,
    title: data.name,
    type: 'scene',
    pics: [data.pic],
    tags: data.tags,
    item_id: data.id,
    poi: {
      name: data.name,
      lat: data.lat,
      lng: data.lng,
      price: formatMoney(data.avg_price, 2, "")
    }
  }
}

function checkExist(id) {
  const section = globalStore.editPlan.sections[selectDay.value]

  const index = section.timeline.findIndex(item => item.type === 'scene' && item.item_id === parseInt(id))
  return index > -1
}

async function onSubmit() {
  if (selectIds.value.length === 0) {
    return
  }

  showLoading()

  for (const id of Object.keys(selectIds.value)) {
    if (checkExist(id)) {
      continue
    }

    const data = await packItem(id)
    globalStore.editPlan.sections[selectDay.value].timeline.push(data)
  }
  hideLoading()

  navBack()
}

function joinSelectPoiText(item) {
  if (!selectIds.value[item.id]) {
    return ''
  }

  return '已添加至第' + selectIds.value[item.id].days.map(item => item + 1).join(',') + '天'
}

function onSelectPoi(id, index, name) {
  if (selectIds.value[id]) {
    delete selectIds.value[id]
  } else {
    selectIds.value[id] = {
      id,
      days: [index],
      name,
    }
  }
}

function initSelectPoi() {
  if (globalStore.editPlan) {
    globalStore.editPlan.sections.forEach((section, index) => {
      if (index !== selectDay.value) {
        return
      }
      section.timeline.forEach(item => {
        if (item.item_id > 0 && item.type === 'scene') {
          onSelectPoi(item.item_id, index, item.poi.name)
        }
      })
    })
  }
}

async function onListItemTap(selectItem) {
  if (isSelecting.value) {
    return
  }

  try {
    isSelecting.value = true
    if (type.value == 'add') {
      if (selectIds.value[selectItem.id]) {
        delete selectIds.value[selectItem.id]
      } else {
        onSelectPoi(selectItem.id, selectDay.value, selectItem.name)
      }
    } else {
      for (const [li, item] of globalStore.editPlan.sections[selectDay.value].timeline.entries()) {
        if (item.item_id == objId.value) {
          const data = await packItem(selectItem.id)

          globalStore.editPlan.sections[selectDay.value].timeline[li] = data
          navBack()
          break
        }
      }
    }
  } finally {
    isSelecting.value = false
  }
}

function getList() {
  if (isLoading.value) {
    return
  }

  const params = {page_size, page: page.value, ...search.value}
  isLoading.value = true

  sceneSearch(params, false).then(({data}) => {
    list.value.push(...data.list)
    total.value = data.total
  }).finally(() => isLoading.value = false)
}

function isIncludes(item) {
  return !!selectIds.value[item.id]
}

onReachBottom(() => {
  if (isLoading.value || !hasMore.value) {
    return
  }

  page.value++
  getList()
})

function initZones() {
  const zones = globalStore.editPlan.sections[selectDay.value]?.zones
  if (!zones || zones.length === 0) {
    return
  }
  search.value.zone_ids = zones[0].id
}

onLoad(query => {
  selectDay.value = Number.parseInt(query.day ?? 0)
  type.value = query.action ?? 'replace'
  objId.value = query.id ?? 0

  initZones()

  getList()
  initSelectPoi()
})

</script>

<style lang="scss" scoped>
@import 'scene.scss';
</style>
