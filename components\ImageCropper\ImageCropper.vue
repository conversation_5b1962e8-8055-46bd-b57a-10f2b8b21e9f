<template>
    <view class="image-cropper">
        <!-- 默认上传图片区域 -->
        <view v-if="!isShowImg" class="upload-area" @click="getImage">
            <view class="upload-content">
                <view class="upload-icon">📷</view>
                <view class="upload-text">点击上传图片</view>
            </view>
        </view>

        <!-- 比例选择器 -->
        <view v-if="isShowImg" class="aspect-ratio-selector">
            <view class="ratio-buttons">
                <view v-for="ratio in aspectRatioOptions" :key="ratio.value" class="ratio-button"
                    :class="{ active: selectedAspectRatio === ratio.value }" @click="changeAspectRatio(ratio.value)">
                    {{ ratio.label }}
                </view>
            </view>
        </view>

        <!-- 图片裁剪区域 -->
        <view v-if="isShowImg" class="cropper-container">
            <view class="uni-corpper"
                :style="'width:' + cropperInitW + 'px;height:' + cropperInitH + 'px;background:#000'">
                <view class="uni-corpper-content"
                    :style="'width:' + cropperW + 'px;height:' + cropperH + 'px;left:' + cropperL + 'px;top:' + cropperT + 'px'">
                    <image :src="imageSrc"
                        :style="'width:' + cropperW + 'px;height:' + cropperH + 'px;object-fit:fill;'"></image>
                    <!-- 遮罩层 -->
                    <view class="crop-mask mask-top" :style="maskTopStyle"></view>
                    <view class="crop-mask mask-bottom" :style="maskBottomStyle"></view>
                    <view class="crop-mask mask-left" :style="maskLeftStyle"></view>
                    <view class="crop-mask mask-right" :style="maskRightStyle"></view>
                    <view class="uni-corpper-crop-box" @touchstart.stop="contentStartMove"
                        @touchmove.stop="contentMoveing" @touchend.stop="contentTouchEnd"
                        :style="'left:' + cutL + 'px;top:' + cutT + 'px;right:' + cutR + 'px;bottom:' + cutB + 'px'">
                        <view class="uni-cropper-view-box">
                            <view class="uni-cropper-dashed-h"></view>
                            <view class="uni-cropper-dashed-v"></view>
                            <view class="uni-cropper-line-t" data-drag="top" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-line-r" data-drag="right" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-line-b" data-drag="bottom" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-line-l" data-drag="left" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <!-- 边中间的扁平拖动条 -->
                            <view class="uni-cropper-drag-bar drag-bar-t" data-drag="top" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-drag-bar drag-bar-r" data-drag="right" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-drag-bar drag-bar-b" data-drag="bottom"
                                @touchstart.stop="dragStart" @touchmove.stop="dragMove" @touchend.stop="dragEnd"></view>
                            <view class="uni-cropper-drag-bar drag-bar-l" data-drag="left" @touchstart.stop="dragStart"
                                @touchmove.stop="dragMove"></view>
                            <!-- 四个顶点的圆形控制点 -->
                            <view class="uni-cropper-corner-point point-tr" data-drag="topRight"
                                @touchstart.stop="dragStart" @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-corner-point point-rb" data-drag="rightBottom"
                                @touchstart.stop="dragStart" @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-corner-point point-bl" data-drag="bottomLeft"
                                @touchstart.stop="dragStart" @touchmove.stop="dragMove"></view>
                            <view class="uni-cropper-corner-point point-lt" data-drag="leftTop"
                                @touchstart.stop="dragStart" @touchmove.stop="dragMove"></view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部按钮区域 -->
        <view class='bottom-buttons'>
            <!-- 默认状态按钮（无图片时） -->
            <view v-if="!isShowImg" class="button-row">
                <view class="button-item" @click="handleCancel">取消</view>
                <view class="button-item primary" @click="handleConfirm">确定</view>
            </view>

            <!-- 裁剪状态按钮（有图片时） -->
            <view v-if="isShowImg" class="button-row">
                <view class="button-item" @click="handleCancel">取消</view>
                <view class="button-item" @click="changeImage">换一张</view>
                <view class="button-item primary" @click="getImageInfo">确定</view>
            </view>
        </view>

        <canvas canvas-id="myCanvas" :width="canvasRealW" :height="canvasRealH"
            :style="'position:absolute; width:' + canvasRealW + 'px;height:' + canvasRealH + 'px;top:-9999px;left:-9999px;'"></canvas>
    </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'

// 系统信息和常量
const sysInfo = uni.getSystemInfoSync();
const { proxy } = getCurrentInstance();
const SCREEN_WIDTH = sysInfo.screenWidth
const SAFE_MARGIN = 30 // 安全边距，避免裁切控制点贴近屏幕边缘
const AVAILABLE_WIDTH = SCREEN_WIDTH - SAFE_MARGIN * 2 // 可用显示宽度
const DRAFG_MOVE_RATIO = 1 // 移动时候的比例
const DRAW_IMAGE_W = sysInfo.screenWidth // 设置生成的图片宽度

// 全局变量
let PAGE_X, PAGE_Y, T_PAGE_X, T_PAGE_Y, CUT_L, CUT_T, CUT_R, CUT_B
let IMG_RATIO, IMG_REAL_W, IMG_REAL_H, IMG_ORIGINAL_W, IMG_ORIGINAL_H

// 定义 props
const props = defineProps({
    // 裁剪比例，支持字符串格式：'free'(自由裁剪), '1:1', '16:9', '9:16', '3:2', '2:3'
    aspectRatio: {
        type: String,
        default: 'free'
    },
    // 输出图片质量
    quality: {
        type: Number,
        default: 1
    },
    // 最大输出宽度
    maxWidth: {
        type: Number,
        default: 750 // 默认值，实际会使用屏幕宽度
    }
})

// 定义 emits
const emit = defineEmits(['confirm', 'cancel', 'imageChange'])

// 处理 maxWidth 的实际默认值
const actualMaxWidth = props.maxWidth === 750 ? sysInfo.screenWidth : props.maxWidth

// 比例选择器选项
const aspectRatioOptions = ref([
    { label: '自由', value: 'free' },
    { label: '1:1', value: '1:1' },
    { label: '16:9', value: '16:9' },
    { label: '9:16', value: '9:16' },
    { label: '3:2', value: '3:2' },
    { label: '2:3', value: '2:3' }
])

// 当前选择的比例
const selectedAspectRatio = ref(props.aspectRatio)

// 响应式数据
const imageSrc = ref('')
const isShowImg = ref(false)

// 初始化的宽高（保持屏幕宽度作为容器，但图片内容会有安全边距）
const cropperInitW = ref(SCREEN_WIDTH)
const cropperInitH = ref(SCREEN_WIDTH)

// 动态的宽高
const cropperW = ref(AVAILABLE_WIDTH)
const cropperH = ref(AVAILABLE_WIDTH)

// 动态的left top值
const cropperL = ref(0)
const cropperT = ref(0)

// 图片缩放值
const scaleP = ref(0)
const imageW = ref(0)
const imageH = ref(0)

// Canvas高分辨率配置
const canvasRealW = ref(0)
const canvasRealH = ref(0)

// 裁剪框 宽高
const cutL = ref(0)
const cutT = ref(0)
const cutB = ref(SCREEN_WIDTH)
const cutR = ref('100%')
const qualityWidth = ref(DRAW_IMAGE_W)
const innerAspectRadio = ref(DRAFG_MOVE_RATIO)

// 计算属性
// 上遮罩层样式
const maskTopStyle = computed(() => {
    return `position: absolute; top: 0; left: 0; width: 100%; height: ${cutT.value}px; background: rgba(0, 0, 0, 0.5); z-index: 1;`
})

// 下遮罩层样式
const maskBottomStyle = computed(() => {
    return `position: absolute; bottom: 0; left: 0; width: 100%; height: ${cutB.value}px; background: rgba(0, 0, 0, 0.5); z-index: 1;`
})

// 左遮罩层样式
const maskLeftStyle = computed(() => {
    const topOffset = cutT.value;
    const height = cropperH.value - cutT.value - cutB.value;
    return `position: absolute; top: ${topOffset}px; left: 0; width: ${cutL.value}px; height: ${height}px; background: rgba(0, 0, 0, 0.5); z-index: 1;`
})

// 右遮罩层样式
const maskRightStyle = computed(() => {
    const topOffset = cutT.value;
    const height = cropperH.value - cutT.value - cutB.value;
    return `position: absolute; top: ${topOffset}px; right: 0; width: ${cutR.value}px; height: ${height}px; background: rgba(0, 0, 0, 0.5); z-index: 1;`
})

// 比例相关函数
// 将字符串比例转换为数字比例
const parseAspectRatio = (ratioStr) => {
    if (ratioStr === 'free') return 0
    const [width, height] = ratioStr.split(':').map(Number)
    return width / height
}

// 根据比例和图片尺寸计算最佳裁剪框尺寸
const calculateCropBoxSize = (aspectRatio, imgW, imgH) => {
    if (aspectRatio === 0) {
        // 自由裁剪，返回80%的图片尺寸
        return {
            width: imgW * 0.8,
            height: imgH * 0.8
        }
    }

    // 固定比例裁剪
    const imgRatio = imgW / imgH
    let cropW, cropH

    if (aspectRatio >= imgRatio) {
        // 裁剪比例比图片比例更宽，以图片宽度为准
        cropW = imgW
        cropH = imgW / aspectRatio
    } else {
        // 裁剪比例比图片比例更高，以图片高度为准
        cropH = imgH
        cropW = imgH * aspectRatio
    }

    return { width: cropW, height: cropH }
}

// 比例切换处理函数
const changeAspectRatio = (newRatio) => {
    selectedAspectRatio.value = newRatio
    if (isShowImg.value) {
        // 重新计算裁剪框
        updateCropBoxForAspectRatio()
    }
}

// 根据新比例更新裁剪框
const updateCropBoxForAspectRatio = () => {
    const aspectRatio = parseAspectRatio(selectedAspectRatio.value)
    const cropSize = calculateCropBoxSize(aspectRatio, IMG_REAL_W, IMG_REAL_H)

    // 计算居中位置
    const cutLVal = (IMG_REAL_W - cropSize.width) / 2
    const cutRVal = (IMG_REAL_W - cropSize.width) / 2
    const cutTVal = (IMG_REAL_H - cropSize.height) / 2
    const cutBVal = (IMG_REAL_H - cropSize.height) / 2

    setData({
        cutL: cutLVal,
        cutR: cutRVal,
        cutT: cutTVal,
        cutB: cutBVal
    })
}

// 工具函数
const setData = (obj) => {
    Object.keys(obj).forEach((key) => {
        // 在 Composition API 中，我们需要直接更新对应的 ref
        switch (key) {
            case 'imageSrc': imageSrc.value = obj[key]; break;
            case 'isShowImg': isShowImg.value = obj[key]; break;
            case 'cropperW': cropperW.value = obj[key]; break;
            case 'cropperH': cropperH.value = obj[key]; break;
            case 'cropperL': cropperL.value = obj[key]; break;
            case 'cropperT': cropperT.value = obj[key]; break;
            case 'cutL': cutL.value = obj[key]; break;
            case 'cutT': cutT.value = obj[key]; break;
            case 'cutR': cutR.value = obj[key]; break;
            case 'cutB': cutB.value = obj[key]; break;
            case 'imageW': imageW.value = obj[key]; break;
            case 'imageH': imageH.value = obj[key]; break;
            case 'scaleP': scaleP.value = obj[key]; break;
            case 'qualityWidth': qualityWidth.value = obj[key]; break;
            case 'innerAspectRadio': innerAspectRadio.value = obj[key]; break;
            case 'canvasRealW': canvasRealW.value = obj[key]; break;
            case 'canvasRealH': canvasRealH.value = obj[key]; break;
        }
    });
}

const getImage = () => {
    uni.chooseImage({
        success: function (res) {
            setData({
                imageSrc: res.tempFilePaths[0],
            })
            loadImage();
            emit('imageChange', res.tempFilePaths[0]);
        },
    })
}

// 取消操作
const handleCancel = () => {
    emit('cancel');
}

// 确定操作
const handleConfirm = () => {
    if (isShowImg.value) {
        // 有图片时，生成裁剪后的图片
        getImageInfo();
    } else {
        // 无图片时，提示用户先选择图片
        uni.showToast({
            title: '请先选择图片',
            icon: 'none'
        });
    }
}

// 换一张图片
const changeImage = () => {
    getImage();
}

const loadImage = () => {
    uni.showLoading({
        title: '图片加载中...',
    })

    uni.getImageInfo({
        src: imageSrc.value,
        success: function success(res) {
            // 保存图片真实原始尺寸
            IMG_ORIGINAL_W = res.width
            IMG_ORIGINAL_H = res.height
            IMG_RATIO = res.width / res.height

            // 计算显示尺寸（用于UI布局，使用可用宽度避免贴近屏幕边缘）
            if (IMG_RATIO >= 1) {
                IMG_REAL_W = AVAILABLE_WIDTH
                IMG_REAL_H = AVAILABLE_WIDTH / IMG_RATIO
            } else {
                IMG_REAL_W = AVAILABLE_WIDTH * IMG_RATIO
                IMG_REAL_H = AVAILABLE_WIDTH
            }

            // 根据图片的宽高显示不同的效果   保证图片可以正常显示
            if (IMG_RATIO >= 1) {
                // 根据选择的比例计算裁剪框尺寸
                const aspectRatio = parseAspectRatio(selectedAspectRatio.value)
                const cropSize = calculateCropBoxSize(aspectRatio, AVAILABLE_WIDTH, AVAILABLE_WIDTH / IMG_RATIO)

                const cropperWVal = AVAILABLE_WIDTH;
                const cropperHVal = AVAILABLE_WIDTH / IMG_RATIO;
                let cutLVal = (cropperWVal - cropSize.width) / 2;
                let cutRVal = (cropperWVal - cropSize.width) / 2;
                let cutTVal = (cropperHVal - cropSize.height) / 2;
                let cutBVal = (cropperHVal - cropSize.height) / 2;
                // 计算canvas尺寸：使用原始尺寸保持最高质量
                const canvasRealWVal = Math.round(IMG_ORIGINAL_W);
                const canvasRealHVal = Math.round(IMG_ORIGINAL_H);

                setData({
                    cropperW: AVAILABLE_WIDTH,
                    cropperH: AVAILABLE_WIDTH / IMG_RATIO,
                    // 初始化left right（添加安全边距，居中显示）
                    cropperL: SAFE_MARGIN,
                    cropperT: Math.ceil((SCREEN_WIDTH - AVAILABLE_WIDTH / IMG_RATIO) / 2),
                    cutL: cutLVal,
                    cutT: cutTVal,
                    cutR: cutRVal,
                    cutB: cutBVal,
                    // 图片缩放值
                    imageW: IMG_REAL_W,
                    imageH: IMG_REAL_H,
                    scaleP: IMG_REAL_W / SCREEN_WIDTH,
                    qualityWidth: DRAW_IMAGE_W,
                    innerAspectRadio: IMG_RATIO,
                    // Canvas高分辨率尺寸
                    canvasRealW: canvasRealWVal,
                    canvasRealH: canvasRealHVal
                })
            } else {
                // 根据选择的比例计算裁剪框尺寸
                const aspectRatio = parseAspectRatio(selectedAspectRatio.value)
                const cropSize = calculateCropBoxSize(aspectRatio, AVAILABLE_WIDTH * IMG_RATIO, AVAILABLE_WIDTH)

                const cropperWVal = AVAILABLE_WIDTH * IMG_RATIO;
                const cropperHVal = AVAILABLE_WIDTH;
                let cutLVal = (cropperWVal - cropSize.width) / 2;
                let cutRVal = (cropperWVal - cropSize.width) / 2;
                let cutTVal = (cropperHVal - cropSize.height) / 2;
                let cutBVal = (cropperHVal - cropSize.height) / 2;
                // 计算canvas尺寸：使用原始尺寸保持最高质量
                const canvasRealWVal = Math.round(IMG_ORIGINAL_W);
                const canvasRealHVal = Math.round(IMG_ORIGINAL_H);

                setData({
                    cropperW: AVAILABLE_WIDTH * IMG_RATIO,
                    cropperH: AVAILABLE_WIDTH,
                    // 初始化left right（添加安全边距，居中显示）
                    cropperL: Math.ceil((SCREEN_WIDTH - AVAILABLE_WIDTH * IMG_RATIO) / 2),
                    cropperT: SAFE_MARGIN,

                    cutL: cutLVal,
                    cutT: cutTVal,
                    cutR: cutRVal,
                    cutB: cutBVal,
                    // 图片缩放值
                    imageW: IMG_REAL_W,
                    imageH: IMG_REAL_H,
                    scaleP: IMG_REAL_W / SCREEN_WIDTH,
                    qualityWidth: DRAW_IMAGE_W,
                    innerAspectRadio: IMG_RATIO,
                    // Canvas高分辨率尺寸
                    canvasRealW: canvasRealWVal,
                    canvasRealH: canvasRealHVal
                })
            }
            setData({
                isShowImg: true
            })
            uni.hideLoading()
        }
    })
}

// 拖动时候触发的touchStart事件
const contentStartMove = (e) => {
    PAGE_X = e.touches[0].pageX
    PAGE_Y = e.touches[0].pageY
}

// 拖动时候触发的touchMove事件
const contentMoveing = (e) => {
    var dragLengthX = (PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
    var dragLengthY = (PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO
    // 左移
    if (dragLengthX > 0) {
        if (cutL.value - dragLengthX < 0) dragLengthX = cutL.value
    } else {
        if (cutR.value + dragLengthX < 0) dragLengthX = -cutR.value
    }

    if (dragLengthY > 0) {
        if (cutT.value - dragLengthY < 0) dragLengthY = cutT.value
    } else {
        if (cutB.value + dragLengthY < 0) dragLengthY = -cutB.value
    }
    setData({
        cutL: cutL.value - dragLengthX,
        cutT: cutT.value - dragLengthY,
        cutR: cutR.value + dragLengthX,
        cutB: cutB.value + dragLengthY
    })

    PAGE_X = e.touches[0].pageX
    PAGE_Y = e.touches[0].pageY
}

const contentTouchEnd = () => {

}

// 获取图片
const getImageInfo = () => {
    uni.showLoading({
        title: '图片生成中...',
    });
    // 将图片写入画布 - 使用原始尺寸保持最高质量
    const ctx = uni.createCanvasContext('myCanvas', proxy);
    console.log('Canvas配置验证:', {
        真实图片尺寸: { w: IMG_ORIGINAL_W, h: IMG_ORIGINAL_H },
        显示尺寸: { w: IMG_REAL_W, h: IMG_REAL_H },
        Canvas物理尺寸: { w: canvasRealW.value, h: canvasRealH.value },
        drawImage尺寸: { w: IMG_ORIGINAL_W, h: IMG_ORIGINAL_H }
    });
    ctx.drawImage(imageSrc.value, 0, 0, IMG_ORIGINAL_W, IMG_ORIGINAL_H);
    // ... 在 getImageInfo 函数中 ...
    ctx.draw(true, () => {
        // 建立正确的UI坐标到Canvas坐标的映射关系
        const scaleX = IMG_ORIGINAL_W / IMG_REAL_W;
        const scaleY = IMG_ORIGINAL_H / IMG_REAL_H;

        // --- 重点修改区域：计算并修正裁剪参数 ---

        // 1. 计算裁剪区域在原图上的坐标和尺寸，并立即取整
        let finalX = Math.round(cutL.value * scaleX);
        let finalY = Math.round(cutT.value * scaleY);
        let finalW = Math.round((IMG_REAL_W - cutL.value - cutR.value) * scaleX);
        let finalH = Math.round((IMG_REAL_H - cutT.value - cutB.value) * scaleY);

        // 2. 进行边界检查，防止计算出的区域超出画布范围
        // 确保起点不会是负数
        finalX = Math.max(0, finalX);
        finalY = Math.max(0, finalY);

        // 确保 裁剪宽度/高度 不会超过 "画布尺寸 - 起点坐标"
        finalW = Math.min(finalW, IMG_ORIGINAL_W - finalX);
        finalH = Math.min(finalH, IMG_ORIGINAL_H - finalY);

        // 确保宽高不是0或负数
        if (finalW <= 0 || finalH <= 0) {
            uni.hideLoading();
            uni.showToast({
                title: '裁剪区域无效',
                icon: 'none'
            });
            return;
        }

        console.log('最终输出 (修正后):', {
            x: finalX,
            y: finalY,
            width: finalW,
            height: finalH
        });

        uni.canvasToTempFilePath({
            x: finalX,
            y: finalY,
            width: finalW,
            height: finalH,
            destWidth: finalW,   // 直接使用修正后的尺寸作为输出尺寸
            destHeight: finalH,
            quality: props.quality,
            canvasId: 'myCanvas',
            success: function (res) {
                console.log('canvasToTempFilePath 成功:', res);
                emit('confirm', res.tempFilePath);
            },
            fail: function (err) {
                uni.showToast({
                    title: '图片生成失败',
                    icon: 'none'
                });
                console.error('canvasToTempFilePath 失败:', err);
            },
            complete: function () {
                uni.hideLoading();
            }
        }, proxy);
    });
}

// 设置大小的时候触发的touchStart事件
const dragStart = (e) => {
    T_PAGE_X = e.touches[0].pageX
    T_PAGE_Y = e.touches[0].pageY
    CUT_L = cutL.value
    CUT_R = cutR.value
    CUT_B = cutB.value
    CUT_T = cutT.value
}

// 在固定比例模式下调整裁剪框尺寸
const adjustCropBoxWithAspectRatio = (newCutL, newCutT, newCutR, newCutB) => {
    const aspectRatio = parseAspectRatio(selectedAspectRatio.value)
    if (aspectRatio === 0) {
        // 自由裁剪，直接返回
        return { cutL: newCutL, cutT: newCutT, cutR: newCutR, cutB: newCutB }
    }

    // 计算当前裁剪框的宽高
    const cropW = cropperW.value - newCutL - newCutR
    const cropH = cropperH.value - newCutT - newCutB
    const currentRatio = cropW / cropH

    if (Math.abs(currentRatio - aspectRatio) < 0.01) {
        // 比例已经正确，直接返回
        return { cutL: newCutL, cutT: newCutT, cutR: newCutR, cutB: newCutB }
    }

    // 需要调整比例
    let adjustedCutL = newCutL, adjustedCutT = newCutT, adjustedCutR = newCutR, adjustedCutB = newCutB

    if (currentRatio > aspectRatio) {
        // 当前太宽，需要减少宽度或增加高度
        const targetW = cropH * aspectRatio
        const widthDiff = cropW - targetW
        adjustedCutL = newCutL + widthDiff / 2
        adjustedCutR = newCutR + widthDiff / 2
    } else {
        // 当前太高，需要减少高度或增加宽度
        const targetH = cropW / aspectRatio
        const heightDiff = cropH - targetH
        adjustedCutT = newCutT + heightDiff / 2
        adjustedCutB = newCutB + heightDiff / 2
    }

    return { cutL: adjustedCutL, cutT: adjustedCutT, cutR: adjustedCutR, cutB: adjustedCutB }
}

// 设置大小的时候触发的touchMove事件
const dragMove = (e) => {
    var dragType = e.target.dataset.drag
    switch (dragType) {
        case 'right':
            var dragLength = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            if (CUT_R + dragLength < 0) dragLength = -CUT_R
            setData({
                cutR: CUT_R + dragLength
            })
            break;
        case 'left':
            var dragLength = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            if (CUT_L - dragLength < 0) dragLength = CUT_L
            if ((CUT_L - dragLength) > (cropperW.value - cutR.value)) dragLength = CUT_L - (cropperW.value - cutR.value)
            setData({
                cutL: CUT_L - dragLength
            })
            break;
        case 'top':
            var dragLength = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO
            if (CUT_T - dragLength < 0) dragLength = CUT_T
            if ((CUT_T - dragLength) > (cropperH.value - cutB.value)) dragLength = CUT_T - (cropperH.value - cutB.value)
            setData({
                cutT: CUT_T - dragLength
            })
            break;
        case 'bottom':
            var dragLength = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO
            if (CUT_B + dragLength < 0) dragLength = -CUT_B
            setData({
                cutB: CUT_B + dragLength
            })
            break;
        case 'rightBottom':
            var dragLengthX = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            var dragLengthY = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO

            if (CUT_B + dragLengthY < 0) dragLengthY = -CUT_B
            if (CUT_R + dragLengthX < 0) dragLengthX = -CUT_R

            // 应用比例约束
            const adjusted = adjustCropBoxWithAspectRatio(CUT_L, CUT_T, CUT_R + dragLengthX, CUT_B + dragLengthY)

            setData({
                cutL: adjusted.cutL,
                cutT: adjusted.cutT,
                cutR: adjusted.cutR,
                cutB: adjusted.cutB
            })
            break;
        case 'leftTop':
            var dragLengthX = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            var dragLengthY = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO

            if (CUT_L - dragLengthX < 0) dragLengthX = CUT_L
            if ((CUT_L - dragLengthX) > (cropperW.value - cutR.value)) dragLengthX = CUT_L - (cropperW.value - cutR.value)
            if (CUT_T - dragLengthY < 0) dragLengthY = CUT_T
            if ((CUT_T - dragLengthY) > (cropperH.value - cutB.value)) dragLengthY = CUT_T - (cropperH.value - cutB.value)

            setData({
                cutL: CUT_L - dragLengthX,
                cutT: CUT_T - dragLengthY
            })
            break;
        case 'topRight':
            var dragLengthX = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            var dragLengthY = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO

            if (CUT_R + dragLengthX < 0) dragLengthX = -CUT_R
            if (CUT_T - dragLengthY < 0) dragLengthY = CUT_T
            if ((CUT_T - dragLengthY) > (cropperH.value - cutB.value)) dragLengthY = CUT_T - (cropperH.value - cutB.value)

            setData({
                cutR: CUT_R + dragLengthX,
                cutT: CUT_T - dragLengthY
            })
            break;
        case 'bottomLeft':
            var dragLengthX = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO
            var dragLengthY = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO

            if (CUT_L - dragLengthX < 0) dragLengthX = CUT_L
            if ((CUT_L - dragLengthX) > (cropperW.value - cutR.value)) dragLengthX = CUT_L - (cropperW.value - cutR.value)
            if (CUT_B + dragLengthY < 0) dragLengthY = -CUT_B

            setData({
                cutL: CUT_L - dragLengthX,
                cutB: CUT_B + dragLengthY
            })
            break;
        default:
            break;
    }
}
</script>

<style lang="scss" scoped>
@import './ImageCropper.scss';
</style>
