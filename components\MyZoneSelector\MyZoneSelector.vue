<template>
	<uni-data-picker v-model="value" :localdata="items" popup-title="请选择城市" placeholder="请选择城市"></uni-data-picker>
</template>

<script setup>
	import {
		computed,
		onMounted,
		ref
	} from 'vue';
	import {
		zones
	} from '../../api';

	const props = defineProps({
		modelValue: {
			type: Object,
			default: () => new Date()
		},
	})
	const emit = defineEmits(['update:modelValue'])

	const items = ref([])
	const value = computed({
		get: () => props.modelValue,
		set: (val) => emit('update:modelValue', val)
	})

	onMounted(() => {
		zones().then(res => {
			const {
				data
			} = res

			items.value = data.list.map(province => {
				return {
					text: province.name,
					value: `${province.id}`,
					children: province.list.map(item => {
						return {
							text: item.name,
							value: `${province.id}-${item.id}`,
						}
					})
				}
			})
		})
	})
</script>

<style lang="scss" scoped>
	:deep(.input-value-border) {
		border: 0;
	}
</style>