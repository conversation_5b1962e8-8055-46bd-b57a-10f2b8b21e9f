export function parseMarkdown(markdown) {
  // 处理前先对特殊字符进行转义
  let html = markdown
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
  
  // 匹配被```包裹的代码块，包括语言标识和换行
  html = html.replace(/```([\s\S]*?)```/g, '');
  
  // 匹配未闭合的代码块标记（不管在哪一行）
  html = html.replace(/```[a-zA-Z]*[\s\S]*?($|(?=<))/g, '');
  
  // 处理标题
  html = html.replace(/#{1,6} (.*$)/gm, (match, content, level) => {
    const hLevel = match.split('#').length - 1;
    return `<h${hLevel}>${content}</h${hLevel}>`;
  });
  
  // 处理列表
  // 无序列表
  html = html.replace(/^\s*-\s+(.*$)/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)+/gms, '<ul>$&</ul>');
  
  // 有序列表
  html = html.replace(/^\s*\d+\.\s+(.*$)/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)+/gms, '<ol>$&</ol>');
  
  // 处理引用
  html = html.replace(/^>\s+(.*$)/gm, '<blockquote>$1</blockquote>');
  
  // 处理代码块
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
  
  // 处理强调
  html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // 处理链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
  
  // 处理图片
  html = html.replace(/!\[([^\]]+)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" />');
  
  // 处理水平线
  html = html.replace(/^---+$/gm, '<hr />');
  
  // 处理段落
  html = html.replace(/\n{2,}/g, '</p><p>');
  html = '<p>' + html + '</p>';
  
  // 移除多余的换行符
  html = html.replace(/\n/g, '<br/>');
  
  return html;
}
