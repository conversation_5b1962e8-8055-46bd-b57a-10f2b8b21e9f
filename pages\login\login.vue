<template>
  <view class="container">
    <image class="top-bg" mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/login-bg.png"></image>

    <view class="center">
      <template v-if="needAvatar">
        <view class="h">
          <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
            <image :src="avatarUrl" mode="widthFix"></image>
          </button>

          <text>点击设置头像</text>
        </view>
        <view class="nickname">
          <text>昵称</text>
          <input placeholder="请输入昵称" type="nickname" @blur="onInputNickname" @input="onInputNickname" />
        </view>
      </template>

      <my-button :disable="loading.value" class="login" form-type="submit" size="small" type="primary" @tap="onLogin">
        登录
      </my-button>

    </view>
    <view class="footer">
      <image mode="widthFix" :src="getCdnUrl('/static/login/logo.png')"></image>
      <text>更懂你的智能旅行规划平台</text>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store/user';
import { navBack, showToast, getCdnUrl } from '@/utils';
import { computed, ref } from 'vue';
import { uniGetProvider, uniLogin } from '@/utils/uni';
import MyButton from "@/components/MyButton/MyButton.vue";
import { onLoad } from "@dcloudio/uni-app";
import { thirdRelUser } from "@/api";
import { imageUpload } from "@/utils/upload";
import { trackEvent } from "@/utils/tracker";
import { useGlobalStore } from "@/store/global";

const globalStore = useGlobalStore()
const userStore = useUserStore()
const formData = ref({
  nickname: '',
  avatar_id: ''
})
const tempFile = ref('')
const avatarUrl = ref(getCdnUrl('/static/avatar.png'))
const loading = ref(false)
const userInfo = ref({
  nickname: '',
  avatar: ''
})

const needAvatar = computed(() => userInfo.value.nickname.length === 0 || userInfo.value.avatar.length === 0)

function onChooseAvatar({ detail }) {
  tempFile.value = detail.avatarUrl
  avatarUrl.value = tempFile.value
}

function onInputNickname(e) {
  formData.value.nickname = e.detail.value
}

function getCode() {
  return new Promise((resolve, reject) => {
    uniGetProvider('oauth').then(async ({ provider }) => {
      const p = provider[0]
      uniLogin({ provider: p }).then(({ code }) => {
        resolve(code)
      })
    })
  })
}

async function onLogin() {
  trackEvent('click_login')

  if (loading.value) {
    return
  }
  if (needAvatar.value) {
    if (formData.value.nickname === '' || !tempFile.value) {
      showToast('请填写昵称和头像')
      return
    }

    const { res_id } = await imageUpload(tempFile.value)
    formData.value.avatar_id = res_id
  }

  loading.value = true
  getCode().then(async (code) => {
    const data = {
      code,
      ...formData.value,
    }
    if (globalStore.shareUid) {
      data.share_uid = globalStore.shareUid
    }

    await userStore.login(data)
    showToast('登录成功').then(() => {
      navBack()
    })
  }).finally(() => {
    loading.value = false
  })
}

function preFetch() {
  loading.value = true
  getCode().then(code => {
    thirdRelUser({ code }).then(({ data }) => {
      Object.assign(userInfo.value, data)
    })
  }).finally(() => {
    loading.value = false
  })
}

onLoad(() => {

  // #ifdef MP
  preFetch()
  // #endif

})

</script>

<style lang="scss" scoped>
@import 'login.scss';
</style>
