import { computed, ref, watch } from 'vue';

/**
 * FlipCard 组件的属性定义
 */
export const flipCardProps = {
  modelValue: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: '200rpx'
  },
  duration: {
    type: Number,
    default: 500
  },
  direction: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  },
  customClass: {
    type: String,
    default: ''
  },
  customStyle: {
    type: Object,
    default: () => ({})
  },
  clickable: {
    type: Boolean,
    default: true
  },
  // 新增属性，控制翻转到背面后是否允许再点击返回正面
  disableFlipBack: {
    type: Boolean,
    default: false
  },
  rotations: {
    type: Number,
    default: 1,
    validator: (value) => value >= 1
  },
  animationType: {
    type: String,
    default: 'simple',
    validator: (value) => ['simple', 'spin'].includes(value)
  },
  easing: {
    type: String,
    default: 'ease'
  },
  // 新增刮刮乐相关属性
  enableScratchEffect: {
    type: Boolean,
    default: false
  },
  scratchDuration: {
    type: Number,
    default: 1000
  }
};

/**
 * FlipCard 组件的事件定义
 */
export const flipCardEmits = ['update:modelValue', 'flip', 'scratchComplete'];

/**
 * FlipCard 组件的逻辑
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件发射函数
 * @returns {Object} - 组件逻辑相关的属性和方法
 */
export function useFlipCard(props, emit) {
  // 动画类名，用于重置动画
  const animationClass = ref('');

  // 刮刮乐相关状态
  const isScratchAnimating = ref(false);
  const showBackContent = ref(!props.enableScratchEffect);
  // 添加一个新的状态来控制背面内容的渐显动画
  const isContentFadingIn = ref(false);

  // 计算内部样式，包括动画属性
  const innerStyle = computed(() => {
    // 如果是简单动画，使用transition
    if (props.animationType === 'simple') {
      return {
        transition: `transform ${props.duration / 1000}s ${props.easing}`
      };
    }

    // 如果是多圈旋转，使用animation
    // 不在这里设置animation属性，而是通过CSS类名控制
    return {};
  });

  // 计算容器样式
  const containerStyle = computed(() => {
    return {
      height: props.height,
      '--rotations': props.rotations,
      '--duration': props.duration / 1000 + 's',
      '--easing': props.easing,
      '--scratch-duration': props.scratchDuration / 1000 + 's',
      ...props.customStyle
    };
  });

  // 计算容器类名
  const containerClass = computed(() => {
    return [
      'flip-card',
      props.customClass,
      { 'flipped': props.modelValue },
      { 'scratch-enabled': props.enableScratchEffect },
      `animation-type-${props.animationType}`,
      animationClass.value
    ];
  });

  // 监听modelValue变化，重置动画
  watch(() => props.modelValue, (newVal, oldVal) => {
    // 只有状态变化时才执行
    if (newVal !== oldVal) {
      // 如果启用了刮刮乐效果
      if (props.enableScratchEffect) {
        if (newVal) {
          // 翻转到背面时，先隐藏背面内容
          showBackContent.value = false;
          // 重置渐显状态
          isContentFadingIn.value = false;

          // 立即触发粒子消散动画，不等待翻转动画完成
          console.log('立即开始粒子消散动画');
          // 先重置状态，确保动画可以重新触发
          isScratchAnimating.value = false;

          // 强制重绘后立即设置为true触发动画
          setTimeout(() => {
            // 同时开始粒子消散动画和背面内容渐显
            isScratchAnimating.value = true;
            // 开始背面内容的渐显动画
            isContentFadingIn.value = true;
            // 显示背面内容，但初始透明度为0，通过动画渐显
            showBackContent.value = true;
            console.log('开始背面内容渐显动画');

            // 等待动画完成后重置动画状态
            setTimeout(() => {
              isScratchAnimating.value = false;
              isContentFadingIn.value = false; // 渐显动画完成
              console.log('粒子消散和背面内容渐显动画完成');
              emit('scratchComplete');
            }, props.scratchDuration); // 等待完整的动画时间
          }, 50); // 等待短暂时间确保重置生效

        } else {
          // 翻转回正面时，重置状态
          isScratchAnimating.value = false;
          isContentFadingIn.value = false;
        }
      }

      // 只有当动画类型不是simple时才需要重置动画
      if (props.animationType !== 'simple') {
        // 移除动画类名
        animationClass.value = '';

        // 在下一帧添加动画类名，强制浏览器重新计算和应用动画
        setTimeout(() => {
          const direction = props.direction === 'horizontal' ? 'horizontal' : 'vertical';
          animationClass.value = newVal ? `animate-spin-${direction}` : '';
        }, 100);
      }
    }
  }, { immediate: false });

  // 监听动画类型和方向变化，重置动画
  watch([() => props.animationType, () => props.direction], () => {
    if (props.modelValue && props.animationType !== 'simple') {
      // 移除动画类名
      animationClass.value = '';

      // 在下一帧添加动画类名
      setTimeout(() => {
        const direction = props.direction === 'horizontal' ? 'horizontal' : 'vertical';
        animationClass.value = `animate-spin-${direction}`;
      }, 10);
    }
  });

  // 在组件初始化时，根据初始状态设置动画类名
  if (props.modelValue && props.animationType !== 'simple') {
    const direction = props.direction === 'horizontal' ? 'horizontal' : 'vertical';
    animationClass.value = `animate-spin-${direction}`;
  }

  // 处理翻转动画结束事件
  function handleTransitionEnd(event) {
    // 只处理翻转动画结束
    if (event.propertyName === 'transform' && props.enableScratchEffect && props.modelValue) {
      // 翻转到背面且启用粒子消散效果时，开始粒子消散动画
      console.log('翻转动画结束，触发粒子消散动画');

      // 先重置状态，确保动画可以重新触发
      isScratchAnimating.value = false;
      isContentFadingIn.value = false;

      // 强制重绘后再设置为true触发动画
      setTimeout(() => {
        // 同时开始粒子消散动画和背面内容渐显
        isScratchAnimating.value = true;
        // 开始背面内容的渐显动画
        isContentFadingIn.value = true;
        // 显示背面内容，但初始透明度为0，通过动画渐显
        showBackContent.value = true;
        console.log('开始背面内容渐显动画（由transitionend触发）');

        // 等待动画完成后重置动画状态
        setTimeout(() => {
          isScratchAnimating.value = false;
          isContentFadingIn.value = false; // 渐显动画完成
          console.log('粒子消散和背面内容渐显动画完成（由transitionend触发）');
          emit('scratchComplete');
        }, props.scratchDuration); // 等待完整的动画时间
      }, 50);
    }
  }

  // 处理点击事件
  function handleClick() {
    // 如果组件不可点击，直接返回
    if (!props.clickable) {
      return;
    }

    // 如果当前是背面且禁用了返回正面的点击，则不处理点击事件
    if (props.modelValue && props.disableFlipBack) {
      return;
    }

    // 否则正常处理点击事件
    emit('update:modelValue', !props.modelValue);
    emit('flip', !props.modelValue);
  }

  return {
    animationClass,
    innerStyle,
    containerStyle,
    containerClass,
    handleClick,
    isScratchAnimating,
    showBackContent,
    isContentFadingIn,
    handleTransitionEnd
  };
}
