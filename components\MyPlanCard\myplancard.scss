@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.travel {
  width: 100%;
  border-radius: 20rpx;
  box-sizing: border-box;
  background: linear-gradient(180deg, #A5EAF9 0%, #EEF9E9 126rpx),
  linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 250, 228, 0.68) 100%);

  .header, .header .from-to, .summary {
    @include center();
  }

  .header {
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;

    justify-content: space-between;
    padding: 30rpx;

    .from-to {
      gap: 4rpx;
      font-size: 34rpx;
      font-weight: bold;

      .iconfont {
        color: #1890FF;
      }

      image {
        width: 68rpx;
      }
    }

    .summary {
      gap: 16rpx;

      view {
        @include center(column);
        padding: 0 14rpx;
      }

      image {
        width: 28rpx;
      }
    }
  }

  .content {
    margin-top: -10rpx;
    padding: 30rpx;
    background: linear-gradient(180deg, rgba(153, 199, 255, 0.2) 0%, rgba(243, 247, 255, 0.2) 100%), #FFFFFF;
    border-radius: 20rpx;
    border: 2rpx solid #ACECF7;
    position: relative;

    .integral-cost {
      position: absolute;
      top: 30rpx;
      right: 0rpx;
      width: 114rpx;
      height: 40rpx;
      background: linear-gradient(90deg, rgba(238, 238, 238, 0) 0%, #C0FAFF 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      text {
        font-size: 20rpx;
        color: #1890FF;
        background: transparent;
      }

      image {
        width: 28rpx;
        height: 28rpx;
        margin-left: 4rpx;
      }
    }
  }

  .replace-container {
    padding: 20rpx;
    @include center();

    :deep(.replan-btn) .iconfont, :deep(.replan-btn .iconfont) {
      background: white;
      color: #52C41A;
      border-radius: 50%;
      width: 32rpx;
      height: 32rpx;
      @include center();
      font-size: 20rpx;
    }
  }

  .image-content {
    padding: 0;
    overflow: hidden;

    .plan-image {
      width: 100%;
      height: 630rpx;
      display: block;
    }
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .list-item {
      display: flex;
      flex-direction: column;

      .subject {
        font-weight: 600;
        margin-bottom: 20rpx;
      }

      .tags {
        margin-top: 10rpx;
        display: flex;
        gap: 16rpx;

        text {
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
          font-size: 20rpx;
          background: #C0FAFF;
          color: #1890FF;
          max-width: 100rpx;
          @include ellipse();

          &:nth-child(2) {
            background: #FFF2C1;
            color: #694209;
          }

          &:nth-child(3) {
            background: #F5DFFF;
            color: #690969;
          }

          &:nth-child(4) {
            background: #E8FFDF;
            color: #466909;
          }
        }
      }
    }
  }

  .buttons {
    margin-top: 30rpx;
    padding-top: 28rpx;
    border-top: 1rpx solid $border-color-v2;
    display: flex;
    justify-content: space-between;

    .left {
      @include center();
      justify-content: flex-start;
      gap: $padding-v2;
    }

    .right {
      .iconfont {
        color: #1890FF;
      }
    }

    text {
      background: white;
      color: $plan-color-3;
    }

    .fav {
      color: $font-color-gray;

      &.active {
        color: $plan-color-1;
      }
    }
  }
}
