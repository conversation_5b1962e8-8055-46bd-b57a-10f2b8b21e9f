@import '@/styles/_define.scss';
@import '@/styles/_mix.scss';
@import '@/styles/tour.scss';

.app-container {
	// height: 100vh;
	padding-bottom: 80rpx;
}
.card {
	margin-top: 0;
	
	.title {
		font-size: $h3;
	}
	.content {
		margin: $padding 0;
	}
}

.name-area {
	.title {
		font-size: $h2;
		@include ellipse(2);
	}
}
.days {
	.tips {
		margin: $padding 0;
		@include center();
		justify-content: space-between;
		
		.left {
			> view {
				&:last-child {
					text {
						margin-left: 5rpx;
					}
					color: $primary-color;
				}
			}
			
		}
	}
}
.peoples {
	.title {
		display: flex;
		
		view {
			color: $gray-color;
			margin-left: $padding;
		}
	}
	
	.tips {
		color: red;
	}
	.plus {
		@include center();
		gap: $padding;
		margin: $padding 0;
	}
	
	.list {
		.item {
			margin-bottom: $padding;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.left {
				@include center();
				gap: calc($padding / 2);
				
				.delete {
					color: red;
				}
			}
			
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}
.popup {
	.card {
		border-radius: 0;
		margin-left: 0;
		margin-right: 0;
	}
	.btn {
		text-align: center;
		margin-bottom: $padding;
	}
}

.footer {
	position: fixed;
	bottom: 0%;
	width: 100%;
	display: flex;
	background: white;

		.left, .apply {
			flex: 1;
			box-sizing: border-box;
			@include center(column);
			text-align: center;
			letter-spacing: 2rpx;
			// padding: 25rpx 0;
		}
	.left {
		align-items: start;
		padding: 20rpx 0 20rpx 20rpx;
		
		.price {
			color: $price-color;
			font-weight: bold;
			font-size: 200%;
		}
	}

	.apply {
		color: white;
		background: #54b93b;
		font-size: 36rpx;
		cursor: pointer;
		background: #df392f;
	}
}