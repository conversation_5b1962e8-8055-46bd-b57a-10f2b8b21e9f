<template>
  <!-- 文本视图 - 当有文本但没有酒店列表时显示 -->
  <view v-if="shouldShowTextView" class="text-view">
    <view class="text-content">{{ markdownText }}</view>
  </view>

  <!-- 酒店列表视图 -->
  <view v-if="shouldShowContainer" class="hotel-list">
    <text class="title">为您推荐以下酒店</text>

    <!-- 酒店列表 - 只有当有内容时才显示 -->
    <view v-if="hasContent" class="list">
      <view v-for="(hotel, index) in hotelList" :key="hotel.id || index" class="hotel-card"
        @tap="onHotelClick(hotel, index)">
        <!-- 图片区域 -->
        <view class="image-section">
          <view v-if="isReplace" class="btn-replace">
            <text class="iconfont icon-qiehuan1"/>
            替换
          </view>
          <image v-if="hotel.coverImage" :src="hotel.coverImage" class="cover-image" lazy-load mode="aspectFill"/>
          <view v-else class="placeholder-image">
            <text class="placeholder-text">酒店图片</text>
          </view>
        </view>
        <!-- 描述区域 -->
        <view class="description-section">
          <!-- 标题和价格行 -->
          <view class="title-price-row">
            <view v-if="hotel.name" class="hotel-title">{{ hotel.name }}</view>
            <view v-if="getHotelPrice(hotel, index)" class="price-tag">￥{{ getHotelPrice(hotel, index) }}</view>
          </view>

          <!-- 评分 -->
          <view class="brand-score-row">
            <view v-if="hotel.score" class="score-rating">
              <view>
                <text v-for="star in getValidScoreValue(hotel.score)" :key="star" class="iconfont icon-shoucang1" />
              </view>
              <text class="score-text">{{ hotel.score }}</text>
            </view>
          </view>

          <!-- 地址 -->
          <view v-if="hotel.fullAddress" class="address">
            <text class="iconfont icon-ditu" />
            <text class="address-text">{{ hotel.fullAddress }}</text>
          </view>

          <!-- 推荐理由 -->
          <view v-if="hotel.recommendation" class="recommendation">
            <view class="recommendation-header">推荐理由</view>
            <view class="recommendation-content">{{ hotel.recommendation }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 查看更多酒店文字 - 只有当有内容时才显示 -->
    <view v-if="showViewMore && hasContent" class="more-hotels-text" @tap="onViewMoreHotels">
      查看更多酒店
      <text class="iconfont icon-gengduo" />
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  markdownText: {
    type: String,
    default: ''
  },
  showViewMore: {
    type: Boolean,
    default: true
  },
  isReplace: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['hotel-click', 'view-more'])

// 酒店列表数据
const hotelList = ref([])

// 空列表状态
const isEmpty = ref(false)

// 解析是否已完成状态（是否遇到结束标记）
const isParsingComplete = ref(false)

// 独立的价格数据映射表，以酒店ID或索引为键 - 避免价格更新影响图片渲染
const hotelPrices = ref(new Map())

// 搜索中心经纬度
const searchCenter = ref({
  longitude: '',
  latitude: ''
})

// 上次解析的文本长度，用于增量解析
const lastParsedLength = ref(0)

// 计算是否有内容可显示
const hasContent = computed(() => {
  return hotelList.value.length > 0
})

// 计算是否应该显示空状态
const isEmptyState = computed(() => {
  return isEmpty.value && hotelList.value.length === 0
})

// 计算是否应该显示文本视图
const shouldShowTextView = computed(() => {
  // 有markdownText内容 且 没有酒店列表内容 且 不是空状态时显示文本视图
  return props.markdownText && !hasContent.value && !isEmptyState.value
})

// 计算是否应该显示酒店列表容器（包括标题）
const shouldShowContainer = computed(() => {
  // 有酒店列表内容时显示列表容器
  return hasContent.value
})

// 获取有效的评分值，用于显示星星数量
function getValidScoreValue(score) {
  if (!score) return 0
  const numScore = Number.parseFloat(score)
  if (isNaN(numScore)) return 0
  // 限制在0-5之间，并向下取整
  return Math.max(0, Math.min(5, Math.floor(numScore)))
}

// 获取酒店价格 - 从独立的价格映射表中获取，避免影响酒店主对象
function getHotelPrice(hotel, index) {
  const key = hotel.id || `hotel_${index}`
  return hotelPrices.value.get(key) || ''
}

// 设置酒店价格到独立映射表
function setHotelPrice(hotel, index, price) {
  if (!price) return
  const key = hotel.id || `hotel_${index}`
  hotelPrices.value.set(key, price)
}

// 创建单个酒店对象 - 价格信息独立管理，不包含在主对象中
function createHotelObject() {
  return {
    coverImage: '',
    name: '',
    id: '',
    brand: '',
    city: '',
    address: '',
    score: '',
    longitude: '',
    latitude: '',
    recommendation: '',
    fullAddress: ''
  }
}

// 更新酒店的完整地址
function updateFullAddress(hotel) {
  const parts = []
  if (hotel.city) parts.push(hotel.city)
  if (hotel.address) parts.push(hotel.address)
  hotel.fullAddress = parts.join(' ')
}

// 检查酒店是否有实质性更新 - 更精确的变更检测，价格独立管理
function isHotelUpdated(oldHotel, newHotel) {
  if (!oldHotel || !newHotel) return true

  // 比较关键字段是否有变化，只有当新值存在且不同时才认为有更新，价格已独立处理
  const keyFields = ['name', 'coverImage', 'score', 'recommendation', 'longitude', 'latitude']

  for (const field of keyFields) {
    // 只有当新值存在且与当前值不同，或当前值为空时才认为有更新
    if (newHotel[field] && (newHotel[field] !== oldHotel[field] || !oldHotel[field])) {
      return true
    }
  }

  return false
}

// 合并酒店数据，保留已有的非空字段 - 直接修改现有对象避免重新渲染
function mergeHotelData(currentHotel, updatedHotel) {
  // 只有当新字段有值且与当前值不同时才更新 - 价格已独立管理，无需处理
  const fieldsToMerge = [
    'coverImage', 'name', 'id', 'brand', 'city', 'address',
    'score', 'longitude', 'latitude', 'recommendation', 'fullAddress'
  ]

  let hasChanges = false

  fieldsToMerge.forEach(field => {
          // 如果新值存在且不为空，或者当前值为空，则更新
      if (updatedHotel[field] && (updatedHotel[field] !== currentHotel[field] || !currentHotel[field])) {
        // 对于图片URL字段，由于已在parseHotelBlock中验证过，直接更新即可
        currentHotel[field] = updatedHotel[field]
        hasChanges = true
      }
  })
  return hasChanges
}

// 检测是否为空列表状态
function checkEmptyState(text) {
  if (!text) {
    isEmpty.value = false
    return
  }

  // 检测空列表关键词
  const emptyKeywords = [
    '暂无符合的酒店',
    '没有找到酒店',
    '未找到酒店',
    '暂无酒店',
    '没有符合条件的酒店',
    '抱歉，没有找到'
  ]

  const hasEmptyKeyword = emptyKeywords.some(keyword => text.includes(keyword))
  
  // 如果明确遇到空列表关键词，直接设置为空状态
  if (hasEmptyKeyword) {
    isEmpty.value = true
    return
  }

  // 检查是否有【推荐酒店】标记但内容为空或只有空文本
  const hotelContent = extractHotelContent(text)
  const hasHotelSection = text.includes('【推荐酒店】')
  const isContentEmpty = !hotelContent || hotelContent.trim().length === 0 || 
                         /^[\s\n]*$/.test(hotelContent)

  // 只有在解析完成且酒店内容确实为空时，才设置为空状态
  // 避免在流式输出过程中因为内容暂时为空而错误显示空状态
  if (hasHotelSection && isContentEmpty && isParsingComplete.value) {
    isEmpty.value = true
  } else if (hasHotelSection && !isContentEmpty) {
    // 如果有酒店标记且有内容，确保不是空状态
    isEmpty.value = false
  }
}

// 解析markdown文本的函数 - 增量解析版本
function parseMarkdownText(text) {
  if (!text) {
    // 如果文本为空，重置所有状态
    hotelList.value = []
    isEmpty.value = false
    lastParsedLength.value = 0
    searchCenter.value = {longitude: '', latitude: ''}
    return
  }

  // 检查是否有新内容需要解析
  if (text.length <= lastParsedLength.value) {
    return // 没有新内容，跳过解析
  }

  // 检测空列表状态
  checkEmptyState(text)

  // 如果是空状态，直接返回，不需要解析酒店
  if (isEmpty.value) {
    lastParsedLength.value = text.length
    return
  }

  // 提取【推荐酒店】到【推荐酒店结束】之间的内容
  const hotelContent = extractHotelContent(text)
  if (!hotelContent) {
    lastParsedLength.value = text.length
    return
  }

  // 按酒店分隔符分割文本，支持多个酒店
  const hotelBlocks = splitIntoHotelBlocks(hotelContent)

  // 增量解析：只处理新增的酒店
  const currentHotelCount = hotelList.value.length
  const newHotelCount = hotelBlocks.length
  if (newHotelCount > currentHotelCount) {
    // 有新酒店需要解析
    for (let i = currentHotelCount; i < newHotelCount; i++) {
      const block = hotelBlocks[i]
      if (block && block.trim()) {
        const result = parseHotelBlock(block, i)
        const {hotel, price} = result
        if (hotel.name || hotel.coverImage) {
          hotelList.value.push(hotel)
          // 独立设置价格到价格映射表
          if (price) {
            setHotelPrice(hotel, i, price)
          }
        }
      }
    }
  } else if (newHotelCount === currentHotelCount && currentHotelCount > 0) {
    // 酒店数量没变，但可能最后一个酒店的内容有更新
    const lastIndex = currentHotelCount - 1
    const lastBlock = hotelBlocks[lastIndex]
    if (lastBlock && lastBlock.trim()) {
      const result = parseHotelBlock(lastBlock, lastIndex)
      const {hotel: updatedHotel, price: updatedPrice} = result
      if (updatedHotel.name || updatedHotel.coverImage) {
        // 检查最后一个酒店是否有实质性更新
        const currentLastHotel = hotelList.value[lastIndex]
        if (isHotelUpdated(currentLastHotel, updatedHotel)) {
          // 关键修复：直接修改现有对象属性，避免对象替换导致的重新渲染
          mergeHotelData(currentLastHotel, updatedHotel)
        }

        // 单独处理价格更新，不影响酒店主对象
        if (updatedPrice) {
          const currentPrice = getHotelPrice(currentLastHotel, lastIndex)
          if (currentPrice !== updatedPrice) {
            setHotelPrice(currentLastHotel, lastIndex, updatedPrice)
          }
        }
      }
    }
  }

  // 更新已解析的文本长度
  lastParsedLength.value = text.length

  // 在解析完酒店后，尝试解析搜索中心经纬度（通常在【选项】部分）
  parseSearchCenter(text)
}

// 解析搜索中心经纬度
function parseSearchCenter(text) {
  const searchCenterMatch = text.match(/搜索中心:\s*([0-9.-]+),([0-9.-]+)/)
  if (searchCenterMatch) {
    const newLongitude = searchCenterMatch[1].trim()
    const newLatitude = searchCenterMatch[2].trim()

    // 只有当值发生变化时才更新，避免不必要的响应式更新
    if (searchCenter.value.longitude !== newLongitude || searchCenter.value.latitude !== newLatitude) {
      searchCenter.value.longitude = newLongitude
      searchCenter.value.latitude = newLatitude
    }
  }
  // 注意：在流式输出中，不要清空之前的值，因为搜索中心可能还没有出现
}

// 提取【推荐酒店】到【推荐酒店结束】之间的内容
function extractHotelContent(text) {
  const startMatch = text.match(/【推荐酒店】/)

  if (startMatch) {
    const startIndex = startMatch.index + startMatch[0].length
    const endMatch = text.match(/【推荐酒店结束】/)

    if (endMatch) {
      // 如果找到结束标记，提取开始到结束之间的内容
      const endIndex = endMatch.index
      // 标记解析已完成
      isParsingComplete.value = true
      return text.substring(startIndex, endIndex).trim()
    } else {
      // 流式输出情况：只有开始标记，没有结束标记
      // 标记解析尚未完成
      isParsingComplete.value = false
      // 提取从开始标记到文本末尾的内容，但排除【选项】部分
      let content = text.substring(startIndex).trim()

      // 如果包含【选项】，则截取到【选项】之前
      const optionMatch = content.match(/【选项】/)
      if (optionMatch) {
        content = content.substring(0, optionMatch.index).trim()
      }

      return content
    }
  }

  // 如果没有找到【推荐酒店】标记，尝试解析整个文本（兼容旧格式）
  // 对于旧格式，假设解析已完成
  isParsingComplete.value = true
  return text
}

// 将文本分割为多个酒店数据块
function splitIntoHotelBlocks(text) {
  // 以数字编号开头的行作为新酒店的开始标志（如 "1." "2." 等）
  const blocks = text.split(/(?=\n?\d+\.\s*\n)/g)
  return blocks.filter(block => block.trim().length > 0)
}

// 验证图片URL是否完整有效 - 基于换行符判断内容完整性
function isValidImageUrl(url, originalMatch) {
  if (!url || typeof url !== 'string') {
    return false
  }
  
  // 检查原始匹配是否以换行符结尾，表示这一行内容已完整
  if (originalMatch && originalMatch.endsWith('\n')) {
    return url.trim().length > 0
  }
  
  return false
}

// 解析单个酒店数据块 - 返回酒店对象和价格信息
function parseHotelBlock(block) {
  const hotel = createHotelObject()
  let price = ''

  // 解析封面图 - 支持链接格式，避免匹配到下一行，并验证URL完整性
  const coverImageMatch = block.match(/封面图：\s*(?:\[)?([^\]\n]*?)(?:\]\([^)]*\))?(?:\n|$)/m)
  if (coverImageMatch && coverImageMatch[1].trim()) {
    const imageUrl = coverImageMatch[1].trim()
    // 只有当图片URL通过完整性验证时才设置（基于换行符判断）
    if (isValidImageUrl(imageUrl, coverImageMatch[0])) {
      hotel.coverImage = imageUrl
    }
    // 如果URL不完整，暂时不设置，等待后续流式输出补全
  }

  // 解析酒店名称
  const nameMatch = block.match(/##\s*([^\n]+?)(?:\n|$)/m)
  if (nameMatch && nameMatch[1].trim()) {
    hotel.name = nameMatch[1].trim()
  }

  // 解析ID - 只匹配到行尾，不跨行
  const idMatch = block.match(/ID：\s*([^\n]*?)(?:\n|$)/m)
  if (idMatch && idMatch[1].trim()) {
    hotel.id = idMatch[1].trim()
  }

  // 解析品牌 - 只匹配到行尾，不跨行
  const brandMatch = block.match(/品牌：\s*([^\n]*?)(?:\n|$)/m)
  if (brandMatch && brandMatch[1].trim()) {
    hotel.brand = brandMatch[1].trim()
  }

  // 解析城市 - 只匹配到行尾，不跨行
  const cityMatch = block.match(/城市：\s*([^\n]*?)(?:\n|$)/m)
  if (cityMatch && cityMatch[1].trim()) {
    hotel.city = cityMatch[1].trim()
  }

  // 解析地址 - 只匹配到行尾，不跨行
  const addressMatch = block.match(/地址：\s*([^\n]*?)(?:\n|$)/m)
  if (addressMatch && addressMatch[1].trim()) {
    hotel.address = addressMatch[1].trim()
  }

  // 解析均价 - 独立处理，不添加到酒店对象中
  const priceMatch = block.match(/均价：\s*([^\n]*?)(?:\n|$)/m)
  if (priceMatch && priceMatch[1].trim()) {
    price = priceMatch[1].trim()
  }

  // 解析评分 - 只匹配到行尾，不跨行，并验证是否为有效评分
  const scoreMatch = block.match(/评分：\s*([^\n]*?)(?:\n|$)/m)
  if (scoreMatch && scoreMatch[1].trim()) {
    const scoreValue = scoreMatch[1].trim()
    // 验证评分是否为有效数字（避免经纬度等其他数据被错误解析为评分）
    if (/^[0-9.]+$/.test(scoreValue) && !scoreValue.includes(',')) {
      hotel.score = scoreValue
    }
  }

  // 解析经纬度 - 只匹配到行尾，不跨行
  const coordinatesMatch = block.match(/经纬度：\s*([^\n]*?)(?:\n|$)/m)
  if (coordinatesMatch && coordinatesMatch[1].trim()) {
    const coordinates = coordinatesMatch[1].trim().split(',')
    if (coordinates.length === 2) {
      hotel.longitude = coordinates[0].trim()
      hotel.latitude = coordinates[1].trim()
    }
  }

  // 解析推荐理由 - 支持多行引用
  const recommendationMatch = block.match(/推荐理由：\s*\n((?:>\s*.+(?:\n|$))+)/m)
  if (recommendationMatch) {
    // 提取所有以 > 开头的行，并去掉 > 符号
    const recommendationLines = recommendationMatch[1]
      .split('\n')
      .filter(line => line.trim().startsWith('>'))
      .map(line => line.replace(/^\s*>\s*/, '').trim())
      .filter(line => line.length > 0)

    hotel.recommendation = recommendationLines.join(' ')
  }

  // 更新完整地址
  updateFullAddress(hotel)
  return { hotel, price }
}

// 重置解析状态
function resetParseState() {
  hotelList.value = []
  isEmpty.value = false // 重置空列表状态
  isParsingComplete.value = false // 重置解析完成状态
  hotelPrices.value.clear() // 清空价格映射表
  lastParsedLength.value = 0
  searchCenter.value = {longitude: '', latitude: ''}
}

// 监听markdown文本变化
watch(() => props.markdownText, (newText, oldText) => {
  // 如果是全新的文本（比如重置测试），重置状态
  if (!newText || (oldText && newText.length < oldText.length)) {
    resetParseState()
  }

  parseMarkdownText(newText)
}, {immediate: true})

// 酒店点击事件
function onHotelClick(hotel, index) {
  emit('hotel-click', { hotel, index })
}

// 查看更多酒店点击事件
function onViewMoreHotels() {
  // 获取经纬度：优先使用搜索中心，否则使用第一个酒店的经纬度
  let longitude = ''
  let latitude = ''
  let source = ''

  if (searchCenter.value.longitude && searchCenter.value.latitude) {
    // 优先使用搜索中心的经纬度
    longitude = searchCenter.value.longitude
    latitude = searchCenter.value.latitude
    source = 'searchCenter'
  } else if (hotelList.value.length > 0 && hotelList.value[0].longitude && hotelList.value[0].latitude) {
    // 如果没有搜索中心，使用第一个酒店的经纬度
    longitude = hotelList.value[0].longitude
    latitude = hotelList.value[0].latitude
    source = 'firstHotel'
  } else {
    source = 'none'
  }

  emit('view-more', {
    longitude,
    latitude,
    source,
    searchCenter: searchCenter.value,
    firstHotel: hotelList.value.length > 0 ? {
      longitude: hotelList.value[0].longitude || '',
      latitude: hotelList.value[0].latitude || ''
    } : null
  })
}
</script>

<style lang="scss" scoped>
@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

// 文本视图样式
.text-view {
  width: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .text-content {
    font-size: 28rpx;
    line-height: 1.6;
    color: #333333;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.hotel-list {
  width: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;

  .title {
    font-weight: bold;
  }

  .list {
    margin-top: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 30rpx;

    .hotel-card {
      margin: 0 auto;
      background: white;
      overflow: hidden;
      cursor: pointer;
    }
  }

  .more-hotels-text {
    @include center();
    justify-content: flex-end;
    font-weight: bold;
    margin-top: 60rpx;
  }
}

.image-section {
  width: 634rpx;
  height: 356rpx;
  position: relative;

  .btn-replace {
    position: absolute;
    right: 0;
    top: 30rpx;
    z-index: 1;
    background: #1890FF;
    border-radius: 30rpx 0rpx 0rpx 30rpx;
    padding: 10rpx 22rpx;
    color: white;
    gap: 4rpx;

    &, .iconfont {
      @include center();
    }

    .iconfont {
      font-size: 20rpx;
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      color: #1890FF;
      background: white;
    }
  }

  .cover-image {
    width: 100%;
    height: 100%;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
  }

  .placeholder-image {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .placeholder-text {
      color: #999999;
      font-size: 24rpx;
    }
  }
}

.description-section {
  background-color: #FFFBE9;
  border: 2rpx solid #FFF3B0;
  border-top: none;
  padding: 20rpx;
  box-sizing: border-box;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

.title-price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.hotel-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  flex: 1;
  margin-right: 20rpx;
}

.price-tag {
  background-color: #333333;
  border-radius: 20rpx;
  padding: 4rpx 24rpx;
  color: white;
  font-size: 24rpx;
  white-space: nowrap;
}

.brand-score-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.brand-rating {
  .brand-text {
    font-size: 20rpx;
    color: #333333;
  }
}

.score-rating {
  @include center();

  >view {
    &:first-child {
      display: flex;
      gap: 8rpx;
      margin-right: 20rpx;

      text {
        color: #F9B414;
      }
    }
  }

  .score-text {
    font-weight: 500;
  }
}

.address {
  margin-bottom: 22rpx;
  @include center();
  justify-content: flex-start;

  .iconfont {
    color: #1890FF;
  }

  .address-text {
    font-size: 20rpx;
    color: #333333;
  }
}

.recommendation {
  background-color: #FFEEAF;
  border-radius: 12rpx;
  padding: 20rpx;
  flex: 1;

  .recommendation-header {
    font-size: 28rpx;
    color: #694209;
    font-weight: 600;
    margin-bottom: 8rpx;
  }

  .recommendation-content {
    font-size: 20rpx;
    color: #333333;
    line-height: 1.4;
  }
}
</style>
