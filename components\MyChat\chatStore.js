import {ref} from 'vue'

export class ChatStore {
  constructor() {
    this.list = ref([]);
  }
  
  removeItem(index) {
    this.list.value.splice(index, 1);
  }
  
  clear() {
    this.list.value = [];
  }
  
  replaceItem(index, data, role, option = null) {
    this.list.value[index] = {
      data,
      role,
      option
    };
  }
  
  addItem(data, role, option = null) {
    this.list.value.push({
      data,
      role,
      option
    });
    
    // 减少最大记录数量和删除阈值，提高滚动性能
    if (this.list.value.length > 50) {
      this.list.value.splice(0, 25);
    }
  }
}
