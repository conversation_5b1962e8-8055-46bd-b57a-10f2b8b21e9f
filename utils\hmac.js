/**
 * HMAC-SHA256 纯JavaScript实现
 * 适用于小程序等没有crypto库的环境
 */

class SHA256 {
  constructor() {
    // SHA-256 初始哈希值
    this.h = [
      0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c,
      0x1f83d9ab, 0x5be0cd19,
    ];

    // SHA-256 常量
    this.k = [
      0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1,
      0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
      0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786,
      0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
      0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147,
      0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
      0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b,
      0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
      0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a,
      0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
      0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2,
    ];
  }

  // 右循环移位
  rotr(n, x) {
    return (x >>> n) | (x << (32 - n));
  }

  // 选择函数
  ch(x, y, z) {
    return (x & y) ^ (~x & z);
  }

  // 多数函数
  maj(x, y, z) {
    return (x & y) ^ (x & z) ^ (y & z);
  }

  // Sigma0 函数
  sigma0(x) {
    return this.rotr(2, x) ^ this.rotr(13, x) ^ this.rotr(22, x);
  }

  // Sigma1 函数
  sigma1(x) {
    return this.rotr(6, x) ^ this.rotr(11, x) ^ this.rotr(25, x);
  }

  // sigma0 函数
  sigma0_small(x) {
    return this.rotr(7, x) ^ this.rotr(18, x) ^ (x >>> 3);
  }

  // sigma1 函数
  sigma1_small(x) {
    return this.rotr(17, x) ^ this.rotr(19, x) ^ (x >>> 10);
  }

  // 将字符串转换为UTF-8字节数组
  stringToUtf8Array(str) {
    const utf8 = [];
    for (let i = 0; i < str.length; i++) {
      let charCode = str.charCodeAt(i);
      if (charCode < 0x80) {
        utf8.push(charCode);
      } else if (charCode < 0x800) {
        utf8.push(0xc0 | (charCode >> 6), 0x80 | (charCode & 0x3f));
      } else if (charCode < 0x10000) {
        utf8.push(
          0xe0 | (charCode >> 12),
          0x80 | ((charCode >> 6) & 0x3f),
          0x80 | (charCode & 0x3f)
        );
      } else {
        utf8.push(
          0xf0 | (charCode >> 18),
          0x80 | ((charCode >> 12) & 0x3f),
          0x80 | ((charCode >> 6) & 0x3f),
          0x80 | (charCode & 0x3f)
        );
      }
    }
    return utf8;
  }

  // 字节数组转换为字符串（用于内部处理）
  bytesToString(bytes) {
    return bytes.map((b) => String.fromCharCode(b)).join("");
  }

  // 消息预处理
  // 消息预处理 (修正版)
  preprocess(message) {
    let msgBytes;
    if (typeof message === "string") {
      msgBytes = this.stringToUtf8Array(message);
    } else {
      msgBytes = message; // 已经是字节数组
    }

    const msgLength = msgBytes.length;
    const msgLengthBits = msgLength * 8;

    // 添加填充位 (第一个位始终为1，即0x80)
    msgBytes.push(0x80);

    // 计算需要填充的零字节数，使得填充后长度 mod 64 等于56
    let zeroPadding = (64 - ((msgBytes.length + 8) % 64)) % 64;
    for (let i = 0; i < zeroPadding; i++) {
      msgBytes.push(0);
    }

    // 正确地添加原始消息长度（64位大端序）
    const highBits = Math.floor(msgLengthBits / 0x100000000);
    const lowBits = msgLengthBits >>> 0;

    // 添加高32位
    for (let i = 3; i >= 0; i--) {
      msgBytes.push((highBits >>> (i * 8)) & 0xff);
    }
    // 添加低32位
    for (let i = 3; i >= 0; i--) {
      msgBytes.push((lowBits >>> (i * 8)) & 0xff);
    }

    return msgBytes;
  }

  // 主哈希函数
  hash(message) {
    const msgBytes = this.preprocess(message);
    const numBlocks = msgBytes.length / 64;

    // 初始化哈希值
    let h0 = this.h[0],
      h1 = this.h[1],
      h2 = this.h[2],
      h3 = this.h[3];
    let h4 = this.h[4],
      h5 = this.h[5],
      h6 = this.h[6],
      h7 = this.h[7];

    // 处理每个512位块
    for (let blockIndex = 0; blockIndex < numBlocks; blockIndex++) {
      const w = new Array(64);

      // 将块分解为16个32位字
      for (let i = 0; i < 16; i++) {
        const start = blockIndex * 64 + i * 4;
        w[i] =
          (msgBytes[start] << 24) |
          (msgBytes[start + 1] << 16) |
          (msgBytes[start + 2] << 8) |
          msgBytes[start + 3];
      }

      // 扩展为64个32位字
      for (let i = 16; i < 64; i++) {
        w[i] =
          (this.sigma1_small(w[i - 2]) +
            w[i - 7] +
            this.sigma0_small(w[i - 15]) +
            w[i - 16]) >>>
          0;
      }

      // 初始化工作变量
      let a = h0,
        b = h1,
        c = h2,
        d = h3,
        e = h4,
        f = h5,
        g = h6,
        h = h7;

      // 主循环
      for (let i = 0; i < 64; i++) {
        const t1 =
          (h + this.sigma1(e) + this.ch(e, f, g) + this.k[i] + w[i]) >>> 0;
        const t2 = (this.sigma0(a) + this.maj(a, b, c)) >>> 0;
        h = g;
        g = f;
        f = e;
        e = (d + t1) >>> 0;
        d = c;
        c = b;
        b = a;
        a = (t1 + t2) >>> 0;
      }

      // 更新哈希值
      h0 = (h0 + a) >>> 0;
      h1 = (h1 + b) >>> 0;
      h2 = (h2 + c) >>> 0;
      h3 = (h3 + d) >>> 0;
      h4 = (h4 + e) >>> 0;
      h5 = (h5 + f) >>> 0;
      h6 = (h6 + g) >>> 0;
      h7 = (h7 + h) >>> 0;
    }

    // 生成最终哈希值
    const hash = [h0, h1, h2, h3, h4, h5, h6, h7];
    return hash.map((h) => h.toString(16).padStart(8, "0")).join("");
  }
}

class HmacSha256 {
  constructor() {
    this.sha256 = new SHA256();
  }

  // HMAC-SHA256 实现
  hmac(key, message) {
    const blockSize = 64; // SHA-256 块大小
    let keyBytes = this.sha256.stringToUtf8Array(key);

    // 如果密钥长度超过块大小，则哈希密钥
    if (keyBytes.length > blockSize) {
      const hashedKey = this.sha256.hash(key);
      keyBytes = [];
      for (let i = 0; i < hashedKey.length; i += 2) {
        keyBytes.push(parseInt(hashedKey.substr(i, 2), 16));
      }
    }

    // 如果密钥长度小于块大小，则用零填充
    while (keyBytes.length < blockSize) {
      keyBytes.push(0);
    }

    // 计算内部和外部填充
    const ipad = new Array(blockSize);
    const opad = new Array(blockSize);

    for (let i = 0; i < blockSize; i++) {
      ipad[i] = keyBytes[i] ^ 0x36;
      opad[i] = keyBytes[i] ^ 0x5c;
    }

    // 内部哈希: hash(K XOR ipad || message)
    const messageBytes = this.sha256.stringToUtf8Array(message);
    const innerInput = ipad.concat(messageBytes);
    const innerHash = this.sha256.hash(innerInput);

    // 将内部哈希转换为字节数组
    const innerHashBytes = [];
    for (let i = 0; i < innerHash.length; i += 2) {
      innerHashBytes.push(parseInt(innerHash.substr(i, 2), 16));
    }

    // 外部哈希: hash(K XOR opad || innerHash)
    const outerInput = opad.concat(innerHashBytes);
    const finalHash = this.sha256.hash(outerInput);

    return finalHash;
  }
}

// 简化的使用函数
function hmacSha256(key, message) {
  const hmac = new HmacSha256();
  return hmac.hmac(key, message);
}

// 简单的SHA-256函数（不带密钥）
function sha256(message) {
  const sha = new SHA256();
  return sha.hash(message);
}
export { sha256, hmacSha256 };
