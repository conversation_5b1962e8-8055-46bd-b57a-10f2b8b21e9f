@use "../../styles/mix";
@use "../../styles/define";

.container {
  padding: 0 define.$padding-page;
  min-height: 100vh;
  background: radial-gradient(circle at 0% 0%, rgba(255, 255, 220, .4) 0%, rgba(255, 255, 255, 0) 50%);
}

.section-list {
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  gap: 30rpx;

  .section-item {
    .subject, .title {
      font-weight: bold;
      @include mix.center();
      justify-content: flex-start;
      gap: 4rpx;
      margin-bottom: 20rpx;
    }

    .subject {
      &:before {
        display: block;
        width: 32rpx;
        height: 32rpx;
        content: '';
        background: #1890FF;
        border-radius: 50%;
      }
    }

    .title {
      font-weight: normal;
      margin-top: 20rpx;

      > view {
        width: 32rpx;
        height: 32rpx;
        @include mix.center();
      }

      text {
        display: block;
        border-radius: 50%;
        background: #1890FF;
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
}