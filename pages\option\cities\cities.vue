<script setup>
import { computed, onBeforeMount, onMounted, ref } from 'vue';
import { citiesList, geo } from '@/api/index'
import MyIndexList from '@/components/MyIndexList/MyIndexList.vue';
import { useGlobalStore } from '@/store/global'
import { navBack } from '@/utils';
import { useHisotryStore } from '@/store/history';
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";

const historyStore = useHisotryStore()
const globalStore = useGlobalStore()
const hots = ref([])
const list = ref([])
const searchText = ref('')
const indexes = computed(() => {
  let listMap = {}
  for (let i = 65; i < 91; i++) {
    listMap[String.fromCharCode(i)] = []
  }
  list.value.forEach((item) => {
    listMap[item.pinyin[0].toUpperCase()].push(item)
  })
  let pre = []
  Object.entries(listMap).forEach(c => {
    pre.push({
      code: c[0],
      list: c[1],
    })
  })
  pre.sort((a, b) => (a.code < b.code ? -1 : 1))
  let out = {
    indexList: [],
    itemArr: [],
  }
  pre.forEach(v => {
    if (v.list.length) {
      out.indexList.push(v.code)
      out.itemArr.push(v.list)
    }
  })
  return out
})
const location = ref({})
const histories = ref(historyStore.cities)
const filteredList = computed(() => {
  const res = list.value.filter(item => {
    const text = searchText.value.toLocaleLowerCase()
    if (text.length == 0) {
      return false
    }

    if (item.province.includes(text) || item.name.includes(text) || item.pinyin.replace(' ', '').includes(text)) {
      return true
    }

    return false
  })
  res.sort((a, b) => {
    const index1 = hots.value.findIndex(item => item.id == a.id)
    const index2 = hots.value.findIndex(item => item.id == b.id)

    return index1 > index2 ? -1 : 1
  })

  return res
})
const indexesHeight = ref('')
const paddingStyle = ref({})
const curLocation = ref({
  city: '获取中...',
})

function onNavBarLoad({ height, paddingBottom }) {
  paddingStyle.value.paddingTop = `${height + paddingBottom + 10}px`

  uni.getSystemInfo({
    success(res) {
      indexesHeight.value = `${res.screenHeight - height - uni.rpx2px(32)}px`
    }
  })
}

function onGetLocation() {
  uni.getLocation({
    success({ latitude, longitude }) {
      geo({ query_lng: longitude, query_lat: latitude }).then(({ data }) => {
        Object.assign(curLocation.value, data)
      })
    }
  })
}

onBeforeMount(async () => {
  const { data } = await citiesList()
  hots.value = data.hots || []
  list.value = data.list || []
})

const doConfirm = (zone) => {
  globalStore.setLocation({
    name: zone.name || zone.city,
    city: zone.name || zone.city,
    zone_id: zone.id || zone.zone_id,
    lat: zone.lat,
    lng: zone.lng
  })
  if (zone.id) {
    historyStore.addCity(zone)
  }

  navBack()
}

onMounted(() => {
  globalStore.getLocation().then(res => {
    Object.assign(location.value, res)
  })

  onGetLocation()
})

</script>

<template>
  <YjNavBar @load="onNavBarLoad">
    <template #center>
      <view class="header">
        <text class="iconfont icon-sousuo1" />
        <input placeholder="城市" @input="({ detail: { value } }) => searchText = value" />
      </view>
    </template>
  </YjNavBar>
  <view :style="paddingStyle" class="container">
    <MyIndexList v-if="!searchText" :height="indexesHeight" :indexes="indexes.indexList" :list="indexes.itemArr"
      first="热门">
      <template #header>
        <view class="box location-box cur-select">
          <view class="title">当前选择城市</view>
          <view class="content">
            <view>
              {{ location.city }}
            </view>
          </view>
        </view>
        <view class="box location-box">
          <view class="title">当前位置</view>
          <view class="content">
            <view @tap="doConfirm(curLocation)">
              <text class="iconfont icon-daohangdizhiweizhi"></text>
              {{ curLocation.city }}
            </view>
          </view>
        </view>
        <view v-if="histories.length > 0" class="box history-box">
          <view class="title">历史搜索</view>
          <view class="content">
            <view v-for="(item, index) in histories" :key="index" @tap="doConfirm(item)">{{ item.name }}</view>
          </view>
        </view>
        <view v-if="hots.length" class="box hots">
          <view class="title">热门城市</view>
          <view class="content">
            <view v-for="(item, i) in hots" :key="item.id" class="hot-item" @tap="doConfirm(item)">{{ item.name }}
            </view>
          </view>
        </view>
      </template>
      <template #default="{ row }">
        <view @tap="doConfirm(row)">{{ row.name }}</view>
      </template>
    </MyIndexList>
    <view v-else class="filter-list">
      <scroll-view :style="{
        height: height
      }" scroll-y enhanced :show-scrollbar="false">
        <view v-for="(city, index) in filteredList" :key="index" class="item" @tap="doConfirm(city)">
          <view>
            <text class="iconfont icon-sousuo1"></text>
            {{ city.name }}
          </view>
          <view>{{ city.province }}</view>
        </view>
      </scroll-view>
    </view>
  </view>

</template>

<style lang="scss" scoped>
@import "cities.scss";
</style>
