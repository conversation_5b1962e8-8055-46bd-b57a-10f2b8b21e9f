<template>
	<view class="loading">
		<view
			:style="{
				background: color,
				height: `${height}px`,
				gap: `${gap}px`
			}"
			v-for="item in count"
			:key="item"
		></view>
	</view>
</template>

<script setup>
const props = defineProps({
	color: {
		type: String,
		default: 'white'
	},
	height: {
		type: Number,
		default: 20
	},
	gap: {
		type: Number,
		default: () => uni.rpx2px(10)
	},
	count: {
		type: Number,
		default: 10
	}
})
</script>

<style lang="scss" scoped>
@import '../../styles/_define.scss';

.loading {
	// text-align: center;
	display: flex;
	gap: 10rpx;

	@keyframes anim {
		0% {
			transform: scaleY(1);
		}

		80% {
			transform: scaleY(0.3);
		}

		90% {
			transform: scaleY(1);
		}
	}

	view {
		width: 6rpx;
		border-radius: $border-radius;
		animation-fill-mode: both;
		animation: anim 0.9s 0s infinite cubic-bezier(0.11, 0.49, 0.38, 0.78);

		&:nth-child(even) {
			animation-delay: 0.25s;
		}

		&:nth-child(odd) {
			animation-delay: 0.5s;
		}
	}
}
</style>
