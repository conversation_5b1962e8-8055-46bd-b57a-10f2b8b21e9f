<template>
	<view class="more-box">
		<text class="text">{{tempContent}}</text>
		<template v-if="showPPP">
			<text class="showMore" v-if="showAll" @click="toggleShow">全部</text>
			<text class="showMore" v-else @click="toggleShow">收起</text>
		</template>
	</view>
</template>

<script setup>
	import {
		ref,
		watch,
		onMounted,
		computed
	} from 'vue';
	const maxSize = 150

	const props = defineProps({
		text: {
			type: String,
			default: '',
		}
	})
	const showAll = ref(false)
	const tempContent = ref('')
	const showPPP = computed(() => props.text.length > maxSize)

	function compute() {
		const length = props.text.length
		if (length > maxSize) {
			showAll.value = true
			tempContent.value = props.text.substr(0, maxSize) + '...'
		} else {
			showAll.value = false
			tempContent.value = props.text
		}
	}

	function toggleShow() {
		if (showAll.value) {
			tempContent.value = props.text
		} else {
			tempContent.value = props.text.substr(0, maxSize) + '...'
		}
		showAll.value = !showAll.value
	}

	watch(() => props.text, (newValue) => {
		compute()
	})

	onMounted(() => compute())
</script>

<style lang="scss" scoped>
	@import '../../styles/_define.scss';

	.more-box {
		.showMore {
			color: $primary-color;
			margin-left: $padding;
		}
	}
</style>