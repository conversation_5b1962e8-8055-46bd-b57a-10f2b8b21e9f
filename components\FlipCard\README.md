# FlipCard 翻牌动画组件

FlipCard是一个实现3D翻转卡片效果的自定义组件，可以传入任何自定义视图作为正面和背面，实现流畅的翻牌动画效果。

## 功能特点

- 支持水平和垂直两种翻转方向
- 可自定义动画持续时间
- 支持通过v-model绑定控制翻转状态
- 支持自定义样式和高度
- 可通过点击触发翻转（可配置）
- 支持多圈旋转，可自定义旋转圈数
- 视图和属性分离，便于维护和扩展

## 使用方法

### 基本用法

```vue
<template>
  <FlipCard v-model="isFlipped" height="300rpx">
    <template #front>
      <view class="front-content">
        <!-- 正面内容 -->
        <text>正面内容</text>
      </view>
    </template>
    <template #back>
      <view class="back-content">
        <!-- 背面内容 -->
        <text>背面内容</text>
      </view>
    </template>
  </FlipCard>
</template>

<script setup>
import { ref } from 'vue';
import FlipCard from '@/components/FlipCard/FlipCard.vue';

const isFlipped = ref(false);
</script>
```

### 垂直翻转

```vue
<FlipCard v-model="isFlipped" height="300rpx" direction="vertical">
  <!-- 插槽内容同上 -->
</FlipCard>
```

### 自定义样式和动画时间

```vue
<FlipCard
  v-model="isFlipped"
  height="300rpx"
  :duration="1000"
  customClass="my-custom-card"
  :customStyle="{ borderRadius: '20rpx' }"
>
  <!-- 插槽内容 -->
</FlipCard>
```

### 禁用点击翻转

```vue
<FlipCard v-model="isFlipped" :clickable="false">
  <!-- 插槽内容 -->
</FlipCard>
```

### 监听翻转事件

```vue
<FlipCard v-model="isFlipped" @flip="onFlip">
  <!-- 插槽内容 -->
</FlipCard>

<script setup>
function onFlip(status) {
  console.log('卡片翻转状态:', status);
}
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| modelValue | Boolean | false | 控制卡片是否翻转，可通过v-model绑定 |
| height | String/Number | '200rpx' | 组件的高度 |
| duration | Number | 500 | 动画持续时间（毫秒） |
| direction | String | 'horizontal' | 翻转方向，可选值：'horizontal'、'vertical' |
| customClass | String | '' | 自定义CSS类 |
| customStyle | Object | {} | 自定义内联样式 |
| clickable | Boolean | true | 是否可以通过点击切换翻转状态 |
| rotations | Number | 1 | 旋转圈数，仅在animationType为'spin'时有效 |
| animationType | String | 'simple' | 动画类型，可选值：'simple'（简单翻转）、'spin'（多圈旋转） |
| easing | String | 'ease' | 动画缓动函数，如'ease'、'ease-in'、'ease-out'、'ease-in-out'、'linear'等 |

## 事件

| 事件名 | 说明 | 参数 |
|-------|------|------|
| update:modelValue | 更新modelValue的值 | 当前的翻转状态 |
| flip | 翻转状态改变时触发 | 当前的翻转状态 |

## 插槽

| 插槽名 | 说明 |
|-------|------|
| front | 卡片正面的内容 |
| back | 卡片背面的内容 |

## 注意事项

1. 在某些平台上，3D变换可能存在兼容性问题，请在目标平台上充分测试。
2. 为了获得最佳效果，建议为正面和背面的内容设置相同的尺寸。
3. 如果内容过于复杂，可能会影响动画性能，请适当优化。
4. 多圈旋转功能在某些低端设备上可能会有性能问题，请根据目标平台适当调整旋转圈数和动画时间。

## 组件结构

组件采用了视图、属性和样式分离的设计模式，主要包含以下文件：

- `FlipCard.vue`：组件的视图部分，包含模板和样式导入
- `useFlipCard.js`：组件的逻辑部分，包含属性定义和核心逻辑
- `FlipCard.scss`：组件的样式部分，包含所有的CSS样式

这种设计模式使组件更加灵活和可维护，同时也便于在其他组件中复用逻辑和样式。
