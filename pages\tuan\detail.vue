<template>
  <view :style="mainStyle" class="app-container">
    <image :src="detail.main_pic" class="main-pic" mode="widthFix"></image>
    <view class="icard">
      <view class="price-bar">
        <view class="price">
          <text class="mini">&yen;</text>
          <text>{{ formatMoney(detail.price, 2, '') }}</text>
          <text class="mini">起</text>
        </view>
        <view class="days">
          <text>{{ detail.days }}天</text>
          <text v-if="detail.days > 1">{{ detail.days - 1 }}夜</text>
        </view>
      </view>
      <view class="name">{{ detail.name }}</view>
      <view v-if="detail.member_limit" class="prop">
        <text class="prop-name">团队人数：</text>
        <text class="prop-value">{{ detail.member_limit }}</text>
      </view>
      <view v-if="detail.age_limit" class="prop">
        <text class="prop-name">报名年龄：</text>
        <text class="prop-value">{{ detail.age_limit }}</text>
      </view>
      <view v-if="detail.base" class="prop">
        <text class="prop-name">集散/解散：</text>
        <text class="prop-value">{{ detail.base }}</text>
      </view>
      <view v-if="detail.customization" class="prop">
        <text class="prop-name">微定制：</text>
        <text class="prop-value">{{ detail.customization }}</text>
      </view>
    </view>
    <view class="icard options">
      <view class="title">选择规格</view>
      <view class="skus">
        <text v-for="(item, i) in detail.skus" :key="i" :class="{ active: formData.sku_id == item.id }" class="sku"
              @tap="doSelectSku(item)">{{
            item.name
          }}
        </text>
      </view>
      <view class="title">选择日期</view>
      <view class="dates">
        <text class="input" @tap="doShowCalendar">
          <text class="iconfont icon-bianji2"></text>
          {{ formData.date ? dayjs(formData.date).format('M月D日') : '设置预约日期' }}
        </text>
      </view>
    </view>

    <view class="tabs">
      <view v-for="item in tabs" :key="item.value" class="tab" @tap="doTab(item.value)">
        <text>{{ item.label }}</text>
        <view :class="{ active: activeTab == item.value }" class="bar"></view>
      </view>
    </view>

    <view :class="{ hide: activeTab != 'desc' }" class="desc">
      <view class="content">
        <rich-text :nodes="richtext(detail.desc)">
        </rich-text>
      </view>
    </view>
    <view :class="{ hide: activeTab != 'trip' }" class="trip">
      <view class="top">
        <text>{{ detail.days }}日行程</text>
        <MyButton :type="tripCollpased ? '' : 'primary'" bordered @tap="tripCollpased = !tripCollpased">{{
            tripCollpased
                ? '展开' : '收起'
          }}行程详情
        </MyButton>
      </view>
      <view v-for="(item, i) in detail.schedules" :key="i" class="schedule">
        <view class="title">{{ item.name }}</view>
        <!--        <view class="day">第{{ i + 1 }}天</view>-->
        <view class="short-desc">{{ item.short_desc }}</view>

        <view v-show="tripCollpased == false" class="content">
          <rich-text :nodes="richtext(item.content)">
          </rich-text>
        </view>
      </view>
    </view>
    <view v-if="activeTab === 'cost'" class="cost">
      <MyCard title="退改政策">
        <view v-for="(txt, index) in detail.refund_rules" :key="index" class="line">{{ txt }}。</view>
      </MyCard>
    </view>
    <view :class="{ hide: activeTab != 'note' }" class="note"></view>
    <view :class="{ hide: activeTab != 'rec' }" class="rec"></view>
    <view :class="{hide:activeTab != 'refund'}" class="refundRule">
      <view v-for="(txt, index) in detail.refund_rules" :key="index" class="line">{{ txt }}。</view>
    </view>

    <view class="footer">
      <view class="left">
        <text>{{ formatMoney(formData.price) }}</text>
      </view>

      <view class="right">
        <MyButton :disable="!!formError" type="primary" @tap="doBuyPop">立即抢购</MyButton>
      </view>
    </view>

    <MyInfoPopup v-if="showCalendar" class="date-dialog" title="选择日期" type="bottom" @close="showCalendar = false">
      <MyCalendar :start="dayjs().startOf('day').add(1, 'day').toDate()" :value="formData.date"
                  type="date" @change="doCalendarChange"
                  @month-change="onMonthChange">
        <template #label="{ date }">
          <text>{{ getSkuPrice(date) }}</text>
        </template>
      </MyCalendar>
    </MyInfoPopup>

    <BuyNumPop v-if="showBuyPop" v-model="formData.num" :price="formData.price" :sku="selectSku"
               @close="showBuyPop = false" @confirm="doBuyConfirm"></BuyNumPop>
  </view>

  <!-- #ifdef MP-WEIXIN -->
  <page-meta :page-style="'overflow:'+(showCalendar?'hidden':'visible')"></page-meta>
  <!-- #endif -->

</template>

<script setup>
import {onLoad, onShareAppMessage} from '@dcloudio/uni-app'
import {computed, ref} from 'vue';
import {productDetail, productSkudates} from '../../api/tuan';
import {formatMoney, navTo, richtext, showToast} from '../../utils/index.js'
import dayjs from 'dayjs';
import {useGlobalStore} from '../../store/global';
import MyButton from '@/components/MyButton/MyButton.vue';
import MyInfoPopup from '@/components/MyInfoPopup/MyInfoPopup.vue';
import MyCalendar from '@/components/MyCalendar/MyCalendar.vue';
import BuyNumPop from './components/BuyNumPop.vue';
import {DateFmtMonth, Disable} from "@/utils/constmap";
import MyCard from "@/components/MyCard/MyCard.vue";

const globalStore = useGlobalStore()
const id = ref(0)
const formData = ref({
  sku_id: 0,
  date: null,
  price: 0, //单价
  num: 1,//数量
})
const showCalendar = ref(false)
const showBuyPop = ref(false)
const detail = ref({
  pics: [],
  name: '',
  desc: '',
  id: '',
  nodes: [],
  skus: [],
  schedules: [],
  refund_rules: [],
})
const skuDates = ref([])
const activeTab = ref('desc')
const tabs = ref([
  {label: '亮点', value: 'desc'},
  {label: '行程', value: 'trip'},
  {label: '费用', value: 'cost'},
  // { label: '须知', value: 'note' },
  // { label: '推荐', value: 'rec' },
  // {label: '退款政策', value: 'refund'},
])
const mainStyle = ref({
  paddingTop: '',
})

const tripCollpased = ref(true)
const dates = computed(() => {
  let dates = []
  let sdates = skuDates.value
  let sku_id = formData.value.sku_id
  if (sku_id == 0) {
    return dates
  }
  sdates.forEach(v => {
    if (v.left_stock <= 0) {
      return
    }
    let sku = v.skus.find(s => s.sku_id == sku_id)
    if (sku) {
      dates.push({
        left_stock: v.left_stock,
        date: v.date,
        price: sku.price,
      })
    }
  })
  return dates
})
const selectSku = computed(() => {
  let sku = {}
  let skus = detail.value.skus
  let {date, sku_id} = formData.value
  let d = dates.value.find(v => dayjs(v.date).isSame(dayjs(date), 'day'))
  if (!d) {
    return sku
  }
  sku.left_stock = d.left_stock
  Object.assign(sku, skus.find(v => v.id == sku_id) || {})
  return sku
})
const formError = computed(() => {
  if (detail.value.state === Disable) {
    return '商品已下架'
  }
  const form = formData.value
  if (!form.sku_id) {
    return '请选择规格'
  }
  if (!form.date) {
    return '请选择预约日期'
  }
  return null
})

function onNavBarLoad({height}) {
  mainStyle.value.paddingTop = `-${height}px`
}

onShareAppMessage(() => {
  return {
    title: detail.value.name,
    path: `pages/tuan/detail?id=${id.value}`
  }
})
const doShowCalendar = () => {
  onMonthChange(dayjs(formData.value?.date || undefined).format(DateFmtMonth))
  showCalendar.value = true
}
const doSelectSku = (item) => {
  Object.assign(formData.value, {
    sku_id: item.id,
    date: null,
    num: 1,
  })
}

const doCalendarChange = (date) => {
  let d = dates.value.find(v => dayjs(v.date).isSame(dayjs(date), 'day'))
  if (!d) {
    return
  }
  formData.value.date = date
  formData.value.price = d.price
  showCalendar.value = false
}

function onMonthChange(month) {
  const start = dayjs(`${month}-15`)//定位当月前后月的15号覆盖到日历跨月显示的情况
  getDate(start.add(-1, 'month').unix(), start.add(1, 'month').endOf('day').unix())
}

const doTab = (val) => {
  activeTab.value = val
  setTimeout(() => {
    uni.pageScrollTo({selector: '.tabs'})
  }, 100)
}

function getSkuPrice(date) {
  let d = dates.value.find(v => dayjs(v.date).isSame(dayjs(date), 'day'))
  if (!d) {
    return ''
  }
  return formatMoney(d.price)
}

function doBuyPop() {
  if (formError.value) {
    showToast(formError.value)
    return
  }
  showBuyPop.value = true
}

function doBuyConfirm() {
  if (formData.value.num < 1) {
    showToast('请选择购买数量')
    return
  }
  let params = {
    product_id: id.value,
    sku_id: formData.value.sku_id,
    date: dayjs(formData.value.date).unix(),
    num: formData.value.num,
  }
  navTo('pages/tuan/apply/apply', params)
}

function getDate(start = dayjs().unix(), end = dayjs().add(5, 'day').unix()) {
  const params = {
    tuan_id: id.value,
    start,
    end,
  }

  productSkudates(params).then(res => {
    const {
      data
    } = res
    skuDates.value = data.list.map(item => ({...item, date: dayjs.unix(item.date).toDate()}))
  })
}


onLoad(query => {
  id.value = query.id

  productDetail(id.value).then(res => {
    const {
      data
    } = res

    data.pics.push(data.main_pic)

    uni.setNavigationBarTitle({
      title: data.name
    })

    Object.assign(detail.value, data)

    Object.assign(mainStyle.value, {
      backgroundImage: `url(${data.main_pic})`,
    })

    let requireIdx = data.skus.findIndex(v => v.is_require)
    if (requireIdx > -1) {
      formData.value.sku_id = data.skus[requireIdx].id
    } else {
      formData.value.sku_id = data.skus[0].id
    }

  })
})
</script>

<style lang="scss" scoped>
@import 'detail.scss';
</style>