<template>
  <view class="app-container">
    <my-wifi-head :name="info.advertiser_name"/>
    <view class="card-container">
      <view class="center">
        <view class="product-name">
          <image :src="info.logo" mode="widthFix"></image>
          {{ info.airline_product_name }}
        </view>
        <view class="code" @tap="onCopy">{{ info.code }}</view>
        <view class="expire">有效期至{{ formatTime(info.expire_time) }}</view>
      </view>

      <view class="data">
        <my-wifi-desc :desc="desc" :scope="info.scope" class="desc-box"/>
      </view>

    </view>
  </view>
</template>

<script setup>
import {computed, ref} from 'vue';
import {onLoad} from '@dcloudio/uni-app'
import {cardGot} from '../../../api/wifi.js'
import {formatTime} from '../../../utils/index.js'
import MyWifiHead from "@/pages/card/components/MyWifiHead.vue";
import MyWifiDesc from "@/pages/card/components/MyWifiDesc.vue";

const info = ref({
  logo: '',
  advertiser_name: '',
  desc: '',
})
const rel_id = ref(0)

const desc = computed(() => {
  if (!info.value.desc) {
    return []
  }

  return info.value.desc.split("\n")
})

function onCopy() {
  uni.setClipboardData({
    data: info.value.code,
  })
}

onLoad((query) => {
  rel_id.value = query.rel_id

  cardGot({
    rel_id: rel_id.value
  }).then(res => {
    const {
      data
    } = res

    Object.assign(info.value, data)
  })
})
</script>

<style lang="scss" scoped>
@import 'got.scss';
</style>